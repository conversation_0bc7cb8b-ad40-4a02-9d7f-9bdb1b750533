-- Enhanced Video Synchronization Migration
-- This migration adds precision timing and better sync control

-- Add new columns to room_video_state for enhanced synchronization
ALTER TABLE room_video_state 
ADD COLUMN IF NOT EXISTS playback_rate DECIMAL(3,2) DEFAULT 1.0,
ADD COLUMN IF NOT EXISTS last_sync_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS sync_version INTEGER DEFAULT 1;

-- Increase precision for timing fields
ALTER TABLE room_video_state 
ALTER COLUMN video_current_time TYPE DECIMAL(12,6),
ALTER COLUMN video_duration TYPE DECIMAL(12,6);

-- Create a function to handle precise video sync updates
CREATE OR REPLACE FUNCTION update_video_sync_state(
  p_room_id UUID,
  p_user_id VARCHAR(255),
  p_is_playing BOOLEAN DEFAULT NULL,
  p_current_time DECIMAL(12,6) DEFAULT NULL,
  p_playback_rate DECIMAL(3,2) DEFAULT NULL,
  p_video_url TEXT DEFAULT NULL,
  p_video_type VARCHAR(20) DEFAULT NULL,
  p_youtube_video_id VARCHAR(50) DEFAULT NULL,
  p_duration DECIMAL(12,6) DEFAULT NULL
) RETURNS room_video_state AS $$
DECLARE
  result room_video_state;
  current_version INTEGER;
BEGIN
  -- Get current sync version
  SELECT sync_version INTO current_version 
  FROM room_video_state 
  WHERE room_id = p_room_id;
  
  -- If no record exists, create one
  IF current_version IS NULL THEN
    current_version := 1;
  ELSE
    current_version := current_version + 1;
  END IF;

  -- Update or insert with precise timestamp
  INSERT INTO room_video_state (
    room_id, 
    updated_by, 
    is_playing, 
    video_current_time, 
    playback_rate,
    video_url,
    video_type,
    youtube_video_id,
    video_duration,
    last_sync_timestamp,
    sync_version,
    updated_at
  ) VALUES (
    p_room_id,
    p_user_id,
    COALESCE(p_is_playing, FALSE),
    COALESCE(p_current_time, 0),
    COALESCE(p_playback_rate, 1.0),
    p_video_url,
    COALESCE(p_video_type, 'url'),
    p_youtube_video_id,
    COALESCE(p_duration, 0),
    NOW(),
    current_version,
    NOW()
  )
  ON CONFLICT (room_id) DO UPDATE SET
    is_playing = COALESCE(p_is_playing, room_video_state.is_playing),
    video_current_time = COALESCE(p_current_time, room_video_state.video_current_time),
    playback_rate = COALESCE(p_playback_rate, room_video_state.playback_rate),
    video_url = COALESCE(p_video_url, room_video_state.video_url),
    video_type = COALESCE(p_video_type, room_video_state.video_type),
    youtube_video_id = COALESCE(p_youtube_video_id, room_video_state.youtube_video_id),
    video_duration = COALESCE(p_duration, room_video_state.video_duration),
    last_sync_timestamp = NOW(),
    sync_version = current_version,
    updated_by = p_user_id,
    updated_at = NOW()
  RETURNING *;

  SELECT * INTO result FROM room_video_state WHERE room_id = p_room_id;
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to calculate precise sync time with network compensation
CREATE OR REPLACE FUNCTION get_precise_sync_time(
  p_room_id UUID,
  p_client_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
) RETURNS JSON AS $$
DECLARE
  video_state room_video_state;
  server_time TIMESTAMP WITH TIME ZONE;
  time_diff INTERVAL;
  calculated_time DECIMAL(12,6);
  result JSON;
BEGIN
  server_time := NOW();
  
  SELECT * INTO video_state 
  FROM room_video_state 
  WHERE room_id = p_room_id;
  
  IF video_state IS NULL THEN
    RETURN json_build_object(
      'error', 'No video state found',
      'server_time', server_time
    );
  END IF;
  
  -- Calculate time difference since last sync
  time_diff := server_time - video_state.last_sync_timestamp;
  
  -- If video is playing, add elapsed time to current position
  IF video_state.is_playing THEN
    calculated_time := video_state.video_current_time + 
                      (EXTRACT(EPOCH FROM time_diff) * video_state.playback_rate);
  ELSE
    calculated_time := video_state.video_current_time;
  END IF;
  
  -- Build response with precise timing data
  result := json_build_object(
    'room_id', video_state.room_id,
    'is_playing', video_state.is_playing,
    'video_current_time', calculated_time,
    'video_duration', video_state.video_duration,
    'playback_rate', video_state.playback_rate,
    'sync_version', video_state.sync_version,
    'server_time', server_time,
    'last_sync_timestamp', video_state.last_sync_timestamp,
    'client_server_diff', EXTRACT(EPOCH FROM (server_time - p_client_timestamp)),
    'video_url', video_state.video_url,
    'video_type', video_state.video_type,
    'youtube_video_id', video_state.youtube_video_id
  );
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION update_video_sync_state TO authenticated;
GRANT EXECUTE ON FUNCTION get_precise_sync_time TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_room_video_state_sync_version ON room_video_state(sync_version);
CREATE INDEX IF NOT EXISTS idx_room_video_state_last_sync ON room_video_state(last_sync_timestamp);

-- Update the existing RLS policies to work with the new functions
DROP POLICY IF EXISTS "Room owners can manage video state" ON room_video_state;
DROP POLICY IF EXISTS "Room members can update video state" ON room_video_state;
DROP POLICY IF EXISTS "Room members can create video state" ON room_video_state;

-- Create new comprehensive policies
CREATE POLICY "Room owners can manage video state" ON room_video_state
  FOR ALL USING (
    room_id IN (
      SELECT id FROM rooms
      WHERE owner_id = current_setting('request.jwt.claims', true)::json->>'sub'
    )
  );

-- Allow room members to read video state
CREATE POLICY "Room members can read video state" ON room_video_state
  FOR SELECT USING (
    room_id IN (
      SELECT room_id FROM room_members
      WHERE user_id = current_setting('request.jwt.claims', true)::json->>'sub'
    )
  );

-- Comment explaining the enhanced sync approach
COMMENT ON FUNCTION update_video_sync_state IS 'Handles precise video synchronization with version control and microsecond timing';
COMMENT ON FUNCTION get_precise_sync_time IS 'Calculates precise video position with network latency compensation';
