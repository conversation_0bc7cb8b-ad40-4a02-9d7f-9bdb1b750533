import { useState, useEffect, useCallback, useRef } from 'react';
import { useUser } from '@clerk/clerk-react';
import { supabase, Room, RoomMember, RoomInvitation } from '@/lib/supabase';
import { toast } from 'sonner';
import { RealtimeChannel } from '@supabase/supabase-js';
import { useSmartRefresh } from './useSmartRefresh';

export interface RoomWithMembers extends Room {
  members: RoomMember[];
  member_count: number;
  user_role?: 'owner' | 'member';
}

export const useRooms = () => {
  const { user } = useUser();
  const [rooms, setRooms] = useState<RoomWithMembers[]>([]);
  const [loading, setLoading] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const channelRef = useRef<RealtimeChannel | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const fetchUserRooms = useCallback(async () => {
    if (!user?.id) {
      setLoading(false);
      setRooms([]);
      return;
    }

    try {
      setLoading(true);

      // Get rooms where user is a member
      const { data: memberData, error: memberError } = await supabase
        .from('room_members')
        .select(`
          room_id,
          role,
          rooms (
            id,
            name,
            description,
            code,
            owner_id,
            created_at,
            updated_at
          )
        `)
        .eq('user_id', user.id);

      if (memberError) throw memberError;

      // Handle case where user has no rooms (memberData is null or empty)
      if (!memberData || memberData.length === 0) {
        setRooms([]);
        setLoading(false);
        return;
      }

      // Get member counts for each room
      const roomIds = memberData.map((m: any) => m.room_id);
      const { data: memberCounts, error: countError } = await supabase
        .from('room_members')
        .select('room_id')
        .in('room_id', roomIds);

      if (countError) throw countError;

      // Process the data
      const roomsWithMembers: RoomWithMembers[] = memberData.map((member: any) => {
        const room = member.rooms as Room;
        const memberCount = memberCounts?.filter((m: any) => m.room_id === room.id).length || 0;

        return {
          ...room,
          members: [],
          member_count: memberCount,
          user_role: member.role
        };
      });

      setRooms(roomsWithMembers);
    } catch (error) {
      console.error('Error fetching rooms:', error);
      toast.error('Failed to load rooms');
      setRooms([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Smart background refresh using utility hook
  useSmartRefresh({
    refreshFunction: fetchUserRooms,
    enabled: !!user?.id,
    connectionStatus,
    idleThreshold: 10000, // 10 seconds for rooms list
    connectedInterval: 300000, // 5 minutes when real-time works
    disconnectedInterval: 120000, // 2 minutes when real-time fails
    debug: false
  });

  // Set up real-time subscriptions for room changes
  useEffect(() => {
    if (!user?.id) {
      setLoading(false);
      setRooms([]);
      return;
    }

    let roomsChannel: RealtimeChannel | null = null;
    let membersChannel: RealtimeChannel | null = null;

    const setupRealtimeSubscriptions = async () => {
      try {
        setConnectionStatus('connecting');
        console.log('Setting up real-time subscriptions for rooms');

        // Initial fetch
        await fetchUserRooms();

        // Subscribe to room changes
        roomsChannel = supabase
          .channel('rooms_changes')
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'rooms'
            },
            (payload) => {
              console.log('Room change detected:', payload);
              // Refresh rooms when any room changes
              fetchUserRooms();
            }
          )
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'room_members'
            },
            (payload) => {
              console.log('Room member change detected:', payload);
              // Refresh rooms when membership changes
              fetchUserRooms();
            }
          )
          .subscribe((status, err) => {
            console.log('Rooms subscription status:', status);
            if (err) {
              console.error('Rooms subscription error:', err);
              setConnectionStatus('error');
              // Retry connection after 5 seconds
              if (reconnectTimeoutRef.current) {
                clearTimeout(reconnectTimeoutRef.current);
              }
              reconnectTimeoutRef.current = setTimeout(() => {
                setupRealtimeSubscriptions();
              }, 5000);
              return;
            }

            if (status === 'SUBSCRIBED') {
              console.log('Successfully subscribed to rooms changes');
              setConnectionStatus('connected');
            } else if (status === 'CHANNEL_ERROR') {
              console.error('Rooms channel error occurred');
              setConnectionStatus('error');
            } else if (status === 'TIMED_OUT') {
              console.error('Rooms subscription timed out');
              setConnectionStatus('error');
            } else if (status === 'CLOSED') {
              console.log('Rooms subscription closed');
              setConnectionStatus('disconnected');
            }
          });

        channelRef.current = roomsChannel;

      } catch (error) {
        console.error('Error setting up real-time subscriptions:', error);
        setConnectionStatus('error');
        toast.error('Failed to connect to real-time updates');
      }
    };

    setupRealtimeSubscriptions();

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (roomsChannel) {
        console.log('Cleaning up rooms subscription');
        supabase.removeChannel(roomsChannel);
      }
      if (membersChannel) {
        console.log('Cleaning up members subscription');
        supabase.removeChannel(membersChannel);
      }
      channelRef.current = null;
      setConnectionStatus('disconnected');
    };
  }, [user?.id, fetchUserRooms]);

  return {
    rooms,
    loading,
    connectionStatus,
    refetch: fetchUserRooms
  };
};

export const useCreateRoom = () => {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);

  const createRoom = async (name: string, description?: string) => {
    if (!user?.id) throw new Error('User not authenticated');

    setLoading(true);
    try {
      // Generate a unique room code
      const code = Math.random().toString(36).substring(2, 8).toUpperCase();

      // Create the room
      const { data: room, error: roomError } = await supabase
        .from('rooms')
        .insert({
          name,
          description,
          code,
          owner_id: user.id
        })
        .select()
        .single();

      if (roomError) throw roomError;

      // Add the creator as the owner member
      const { error: memberError } = await supabase
        .from('room_members')
        .insert({
          room_id: room.id,
          user_id: user.id,
          role: 'owner'
        });

      if (memberError) throw memberError;

      return room;
    } finally {
      setLoading(false);
    }
  };

  return {
    createRoom,
    loading
  };
};

export const useJoinRoom = () => {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);

  const joinRoom = async (code: string) => {
    if (!user?.id) throw new Error('User not authenticated');

    setLoading(true);
    try {
      // Find the room by code
      const { data: room, error: roomError } = await supabase
        .from('rooms')
        .select('*')
        .eq('code', code.toUpperCase())
        .single();

      if (roomError || !room) {
        throw new Error('Room not found');
      }

      // Check if user is already a member
      const { data: existingMember } = await supabase
        .from('room_members')
        .select('*')
        .eq('room_id', room.id)
        .eq('user_id', user.id)
        .single();

      if (existingMember) {
        return room; // Already a member
      }

      // Add user as a member
      const { error: memberError } = await supabase
        .from('room_members')
        .insert({
          room_id: room.id,
          user_id: user.id,
          role: 'member'
        });

      if (memberError) throw memberError;

      return room;
    } finally {
      setLoading(false);
    }
  };

  return {
    joinRoom,
    loading
  };
};

export const useRoom = (roomId: string) => {
  const { user } = useUser();
  const [room, setRoom] = useState<RoomWithMembers | null>(null);
  const [loading, setLoading] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const channelRef = useRef<RealtimeChannel | null>(null);

  const fetchRoom = useCallback(async () => {
    if (!user?.id || !roomId) return;

    try {
      setLoading(true);

      // Get room details and user's membership
      const { data: memberData, error: memberError } = await supabase
        .from('room_members')
        .select(`
          role,
          rooms (
            id,
            name,
            description,
            code,
            owner_id,
            created_at,
            updated_at
          )
        `)
        .eq('room_id', roomId)
        .eq('user_id', user.id)
        .single();

      if (memberError || !memberData) {
        throw new Error('Room not found or access denied');
      }

      // Get all room members
      const { data: allMembers, error: membersError } = await supabase
        .from('room_members')
        .select('*')
        .eq('room_id', roomId);

      if (membersError) throw membersError;

      const roomData = Array.isArray(memberData.rooms) ? memberData.rooms[0] : memberData.rooms;
      const roomWithMembers: RoomWithMembers = {
        ...(roomData as Room),
        members: allMembers || [],
        member_count: allMembers?.length || 0,
        user_role: memberData.role
      };

      setRoom(roomWithMembers);
    } catch (error) {
      console.error('Error fetching room:', error);
      toast.error('Failed to load room');
    } finally {
      setLoading(false);
    }
  }, [user, roomId]);

  // Smart background refresh for individual room data
  useSmartRefresh({
    refreshFunction: fetchRoom,
    enabled: !!(user?.id && roomId),
    connectionStatus,
    idleThreshold: 15000, // 15 seconds for individual room
    connectedInterval: 300000, // 5 minutes when real-time works
    disconnectedInterval: 120000, // 2 minutes when real-time fails
    debug: false
  });

  // Set up real-time subscription for room changes
  useEffect(() => {
    if (!user?.id || !roomId) {
      setLoading(false);
      setRoom(null);
      return;
    }

    let channel: RealtimeChannel | null = null;

    const setupRealtimeSubscription = async () => {
      try {
        setConnectionStatus('connecting');
        console.log('Setting up real-time subscription for room:', roomId);

        // Initial fetch
        await fetchRoom();

        // Subscribe to room and member changes for this specific room
        channel = supabase
          .channel(`room_${roomId}_changes`)
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'rooms',
              filter: `id=eq.${roomId}`
            },
            (payload) => {
              console.log('Room data change detected:', payload);
              fetchRoom();
            }
          )
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'room_members',
              filter: `room_id=eq.${roomId}`
            },
            (payload) => {
              console.log('Room member change detected:', payload);
              fetchRoom();
            }
          )
          .subscribe((status, err) => {
            console.log('Room subscription status:', status);
            if (err) {
              console.error('Room subscription error:', err);
              setConnectionStatus('error');
              return;
            }

            if (status === 'SUBSCRIBED') {
              console.log('Successfully subscribed to room changes');
              setConnectionStatus('connected');
            } else if (status === 'CHANNEL_ERROR') {
              console.error('Room channel error occurred');
              setConnectionStatus('error');
            } else if (status === 'TIMED_OUT') {
              console.error('Room subscription timed out');
              setConnectionStatus('error');
            } else if (status === 'CLOSED') {
              console.log('Room subscription closed');
              setConnectionStatus('disconnected');
            }
          });

        channelRef.current = channel;

      } catch (error) {
        console.error('Error setting up room real-time subscription:', error);
        setConnectionStatus('error');
      }
    };

    setupRealtimeSubscription();

    return () => {
      if (channel) {
        console.log('Cleaning up room subscription');
        supabase.removeChannel(channel);
      }
      channelRef.current = null;
      setConnectionStatus('disconnected');
    };
  }, [user?.id, roomId, fetchRoom]);

  return {
    room,
    loading,
    connectionStatus,
    refetch: fetchRoom
  };
};

export const useRoomManagement = () => {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);

  const updateRoom = async (roomId: string, name: string, description?: string) => {
    if (!user?.id) throw new Error('User not authenticated');

    setLoading(true);
    try {
      const { data: room, error } = await supabase
        .from('rooms')
        .update({
          name,
          description,
          updated_at: new Date().toISOString()
        })
        .eq('id', roomId)
        .eq('owner_id', user.id) // Ensure only owner can update
        .select()
        .single();

      if (error) throw error;
      return room;
    } finally {
      setLoading(false);
    }
  };

  const deleteRoom = async (roomId: string) => {
    if (!user?.id) throw new Error('User not authenticated');

    setLoading(true);
    try {
      const { error } = await supabase
        .from('rooms')
        .delete()
        .eq('id', roomId)
        .eq('owner_id', user.id); // Ensure only owner can delete

      if (error) throw error;
    } finally {
      setLoading(false);
    }
  };

  const transferOwnership = async (roomId: string, newOwnerId: string) => {
    if (!user?.id) throw new Error('User not authenticated');

    setLoading(true);
    try {
      // Check if new owner is a member
      const { data: newOwnerMember, error: memberError } = await supabase
        .from('room_members')
        .select('*')
        .eq('room_id', roomId)
        .eq('user_id', newOwnerId)
        .single();

      if (memberError || !newOwnerMember) {
        throw new Error('New owner must be a member of the room');
      }

      // Update room owner
      const { error: updateRoomError } = await supabase
        .from('rooms')
        .update({
          owner_id: newOwnerId,
          updated_at: new Date().toISOString()
        })
        .eq('id', roomId)
        .eq('owner_id', user.id); // Ensure only current owner can transfer

      if (updateRoomError) throw updateRoomError;

      // Update old owner role to member
      const { error: updateOldOwnerError } = await supabase
        .from('room_members')
        .update({ role: 'member' })
        .eq('room_id', roomId)
        .eq('user_id', user.id);

      if (updateOldOwnerError) throw updateOldOwnerError;

      // Update new owner role to owner
      const { error: updateNewOwnerError } = await supabase
        .from('room_members')
        .update({ role: 'owner' })
        .eq('room_id', roomId)
        .eq('user_id', newOwnerId);

      if (updateNewOwnerError) throw updateNewOwnerError;
    } finally {
      setLoading(false);
    }
  };

  return {
    updateRoom,
    deleteRoom,
    transferOwnership,
    loading
  };
};
