// Quick test script for YouTube URL parsing
import { extractYouTubeVideoId, isYouTubeUrl } from './src/utils/youtube.ts';

const testUrls = [
  'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
  'https://youtu.be/dQw4w9WgXcQ',
  'https://www.youtube.com/embed/dQw4w9WgXcQ',
  'https://m.youtube.com/watch?v=dQw4w9WgXcQ',
  'https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=30s',
  'https://www.youtube.com/watch?list=PLrAXtmRdnEQy6nuLMHjMZOz59Oq8VGLrG&v=dQw4w9WgXcQ',
  'https://www.youtube.com/live/dQw4w9WgXcQ',
  'invalid-url',
  'https://example.com/video.mp4'
];

console.log('Testing YouTube URL parsing:');
testUrls.forEach(url => {
  const videoId = extractYouTubeVideoId(url);
  const isValid = isYouTubeUrl(url);
  console.log(`URL: ${url}`);
  console.log(`  Video ID: ${videoId}`);
  console.log(`  Is YouTube: ${isValid}`);
  console.log('---');
});
