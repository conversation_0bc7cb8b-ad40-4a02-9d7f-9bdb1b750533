import { supabase, createAuthenticatedSupabaseClient } from '@/lib/supabase';

export const testStorageAccess = async (getToken?: any, isSignedIn?: boolean) => {
  try {
    console.log('Testing storage access...');

    // Get the appropriate client
    let client = supabase;
    if (isSignedIn && getToken) {
      try {
        const token = await getToken({ template: 'supabase' });
        if (token) {
          client = createAuthenticatedSupabaseClient(token);
          console.log('Using authenticated client');
        }
      } catch (authError) {
        console.warn('Could not get authenticated client, using anonymous:', authError);
      }
    }

    // Test listing files in the videos bucket
    console.log('Testing file listing...');
    const { data: listData, error: listError } = await client.storage
      .from('videos')
      .list('', { limit: 10 });

    console.log('List result:', { data: listData, error: listError });

    return { success: true, listData, listError };
  } catch (error) {
    console.error('Storage test failed:', error);
    return { success: false, error };
  }
};

export const testStorageDeletion = async (filePath: string, getToken?: any, isSignedIn?: boolean) => {
  try {
    console.log('Testing storage deletion for path:', filePath);

    // Get the appropriate client
    let client = supabase;
    if (isSignedIn && getToken) {
      try {
        const token = await getToken({ template: 'supabase' });
        if (token) {
          client = createAuthenticatedSupabaseClient(token);
          console.log('Using authenticated client for deletion test');
        }
      } catch (authError) {
        console.warn('Could not get authenticated client, using anonymous:', authError);
      }
    }

    // Test deletion
    const { data: deleteData, error: deleteError } = await client.storage
      .from('videos')
      .remove([filePath]);

    console.log('Delete test result:', { data: deleteData, error: deleteError });

    return { success: !deleteError, deleteData, deleteError };
  } catch (error) {
    console.error('Storage deletion test failed:', error);
    return { success: false, error };
  }
};

export const testStorageDetailed = async (getToken?: any, isSignedIn?: boolean) => {
  try {
    console.log('Running detailed storage tests...');

    // Get the appropriate client
    let client = supabase;
    if (isSignedIn && getToken) {
      try {
        const token = await getToken({ template: 'supabase' });
        if (token) {
          client = createAuthenticatedSupabaseClient(token);
          console.log('Using authenticated client for detailed tests');
        }
      } catch (authError) {
        console.warn('Could not get authenticated client, using anonymous:', authError);
      }
    }

    // Test 1: List buckets
    console.log('Test 1: Listing buckets...');
    const { data: buckets, error: listError } = await client.storage.listBuckets();
    if (listError) {
      console.error('Error listing buckets:', listError);
    } else {
      console.log('Available buckets:', buckets?.map(b => ({ id: b.id, name: b.name, public: b.public })));
      const videosBucket = buckets?.find(b => b.id === 'videos');
      if (videosBucket) {
        console.log('Videos bucket found:', videosBucket);
      } else {
        console.error('Videos bucket not found!');
      }
    }

    // Test 2: Try to list files in videos bucket
    console.log('Test 2: Listing files in videos bucket...');
    const { data: files, error: filesError } = await client.storage
      .from('videos')
      .list('', { limit: 5 });

    if (filesError) {
      console.error('Error listing files in videos bucket:', filesError);
    } else {
      console.log('Files in videos bucket:', files);
    }

    // Test 3: Try to get bucket info
    console.log('Test 3: Getting bucket info...');
    const { data: bucketInfo, error: bucketError } = await client.storage
      .from('videos')
      .list('', { limit: 1 });

    if (bucketError) {
      console.error('Error accessing videos bucket:', bucketError);
      return false;
    } else {
      console.log('Videos bucket is accessible');
      return true;
    }

  } catch (error) {
    console.error('Storage test failed:', error);
    return false;
  }
};

// Test function that can be called from the browser console
(window as any).testStorageAccess = testStorageAccess;
