import React, { useState, useEffect } from 'react';
import { RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BackgroundRefreshIndicatorProps {
  /**
   * Whether a refresh is currently happening
   */
  isRefreshing?: boolean;
  
  /**
   * Connection status to show appropriate indicator
   */
  connectionStatus?: 'connecting' | 'connected' | 'disconnected' | 'error';
  
  /**
   * Whether to show the indicator (default: true)
   */
  show?: boolean;
  
  /**
   * Position of the indicator
   */
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  
  /**
   * Custom className
   */
  className?: string;
}

/**
 * A subtle indicator that shows when background refreshes are happening.
 * Only appears briefly and doesn't interrupt user workflow.
 */
export const BackgroundRefreshIndicator: React.FC<BackgroundRefreshIndicatorProps> = ({
  isRefreshing = false,
  connectionStatus = 'disconnected',
  show = true,
  position = 'top-right',
  className
}) => {
  const [visible, setVisible] = useState(false);
  const [lastRefreshTime, setLastRefreshTime] = useState<number | null>(null);

  // Show indicator briefly when refresh starts
  useEffect(() => {
    if (isRefreshing && show) {
      setVisible(true);
      setLastRefreshTime(Date.now());
      
      // Hide after 2 seconds
      const timer = setTimeout(() => {
        setVisible(false);
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [isRefreshing, show]);

  // Don't render if not visible or disabled
  if (!visible || !show) {
    return null;
  }

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      default:
        return 'top-4 right-4';
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'bg-green-500/10 text-green-400 border-green-500/20';
      case 'connecting':
        return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20';
      case 'error':
        return 'bg-red-500/10 text-red-400 border-red-500/20';
      case 'disconnected':
      default:
        return 'bg-blue-500/10 text-blue-400 border-blue-500/20';
    }
  };

  const getMessage = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Syncing...';
      case 'connecting':
        return 'Connecting...';
      case 'error':
        return 'Retrying...';
      case 'disconnected':
      default:
        return 'Updating...';
    }
  };

  return (
    <div
      className={cn(
        'fixed z-50 flex items-center gap-2 px-3 py-2 rounded-lg border backdrop-blur-sm',
        'transition-all duration-300 ease-in-out',
        'text-xs font-medium',
        getPositionClasses(),
        getStatusColor(),
        visible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2',
        className
      )}
    >
      <RefreshCw 
        className={cn(
          'w-3 h-3',
          isRefreshing ? 'animate-spin' : ''
        )} 
      />
      <span>{getMessage()}</span>
    </div>
  );
};

export default BackgroundRefreshIndicator;
