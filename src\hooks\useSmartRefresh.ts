import { useEffect, useRef } from 'react';
import { useRefreshTracker } from './useRefreshTracker';

interface SmartRefreshOptions {
  /**
   * Function to call when refresh is needed
   */
  refreshFunction: () => void;
  
  /**
   * Whether the feature is enabled (e.g., user is logged in, has data to refresh)
   */
  enabled: boolean;
  
  /**
   * Connection status - if 'connected', refresh less frequently
   */
  connectionStatus?: 'connecting' | 'connected' | 'disconnected' | 'error';
  
  /**
   * Minimum idle time before allowing refresh (in milliseconds)
   * @default 15000 (15 seconds)
   */
  idleThreshold?: number;
  
  /**
   * Refresh interval when real-time is working (in milliseconds)
   * @default 300000 (5 minutes)
   */
  connectedInterval?: number;
  
  /**
   * Refresh interval when real-time is not working (in milliseconds)
   * @default 120000 (2 minutes)
   */
  disconnectedInterval?: number;
  
  /**
   * Whether to show debug logs
   * @default false
   */
  debug?: boolean;
}

/**
 * Smart refresh hook that only refreshes when:
 * 1. User has been idle for a specified time
 * 2. Tab is visible
 * 3. Real-time connection is not working (or uses longer intervals when it is)
 * 
 * This prevents disruptive refreshes during active user interaction.
 */
export const useSmartRefresh = (options: SmartRefreshOptions) => {
  const {
    refreshFunction,
    enabled,
    connectionStatus = 'disconnected',
    idleThreshold = 15000, // 15 seconds
    connectedInterval = 300000, // 5 minutes when real-time is working
    disconnectedInterval = 120000, // 2 minutes when real-time is not working
    debug = false
  } = options;

  const lastInteractionTimeRef = useRef(Date.now());
  const isTabVisibleRef = useRef(true);
  const intervalRef = useRef<NodeJS.Timeout>();
  const { wrapRefreshFunction } = useRefreshTracker();

  // Wrap the refresh function to track refresh state
  const trackedRefreshFunction = wrapRefreshFunction(refreshFunction);

  useEffect(() => {
    if (!enabled) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = undefined;
      }
      return;
    }

    // Track user interactions
    const updateInteractionTime = () => {
      lastInteractionTimeRef.current = Date.now();
      if (debug) {
        console.log('User interaction detected, updating last interaction time');
      }
    };

    // Track tab visibility
    const handleVisibilityChange = () => {
      isTabVisibleRef.current = !document.hidden;
      if (debug) {
        console.log('Tab visibility changed:', isTabVisibleRef.current ? 'visible' : 'hidden');
      }
    };

    // Add event listeners for user interactions
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, updateInteractionTime, { passive: true });
    });
    document.addEventListener('visibilitychange', handleVisibilityChange);

    const smartRefresh = () => {
      const timeSinceInteraction = Date.now() - lastInteractionTimeRef.current;
      const isUserIdle = timeSinceInteraction > idleThreshold;
      const isTabVisible = isTabVisibleRef.current;
      const shouldRefresh = isUserIdle && isTabVisible;

      if (debug) {
        console.log('Smart refresh check:', {
          timeSinceInteraction,
          isUserIdle,
          isTabVisible,
          connectionStatus,
          shouldRefresh
        });
      }

      // Different logic based on connection status
      if (connectionStatus === 'connected') {
        // Real-time is working, only refresh occasionally and when idle
        if (shouldRefresh) {
          if (debug) console.log('Smart refresh: Updating data (real-time connected, periodic check)');
          trackedRefreshFunction();
        }
      } else {
        // Real-time is not working, refresh more frequently but still respect user activity
        if (shouldRefresh) {
          if (debug) console.log('Smart refresh: Updating data (real-time disconnected, fallback mode)');
          trackedRefreshFunction();
        }
      }
    };

    // Choose interval based on connection status
    const interval = connectionStatus === 'connected' ? connectedInterval : disconnectedInterval;
    
    if (debug) {
      console.log(`Setting up smart refresh with ${interval}ms interval (connection: ${connectionStatus})`);
    }

    intervalRef.current = setInterval(smartRefresh, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = undefined;
      }
      events.forEach(event => {
        document.removeEventListener(event, updateInteractionTime);
      });
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [
    enabled,
    trackedRefreshFunction,
    connectionStatus,
    idleThreshold,
    connectedInterval,
    disconnectedInterval,
    debug
  ]);
};
