import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const isHttps = process.env.VITE_HTTPS === 'true';

  return {
    server: {
      host: "::",
      port: 8080,
      https: isHttps ? {
        // Use basic self-signed certificate for development
        // This allows YouTube iframe API to work properly
      } : false,
      proxy: {
        '/api': {
          target: 'http://localhost:3001',
          changeOrigin: true,
          secure: false, // Allow proxy to work with self-signed certificates
        },
      },
    },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  };
});
