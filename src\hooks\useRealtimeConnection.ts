import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/lib/supabase';
import { RealtimeChannel } from '@supabase/supabase-js';
import { toast } from 'sonner';

export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

interface RealtimeConnectionOptions {
  channelName: string;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: any) => void;
  autoReconnect?: boolean;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
  showToasts?: boolean;
}

export const useRealtimeConnection = (options: RealtimeConnectionOptions) => {
  const {
    channelName,
    onConnect,
    onDisconnect,
    onError,
    autoReconnect = true,
    maxReconnectAttempts = 5,
    reconnectDelay = 5000,
    showToasts = false
  } = options;

  const [status, setStatus] = useState<ConnectionStatus>('disconnected');
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const channelRef = useRef<RealtimeChannel | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isConnectingRef = useRef(false);

  const cleanup = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    if (channelRef.current) {
      console.log(`Cleaning up channel: ${channelName}`);
      supabase.removeChannel(channelRef.current);
      channelRef.current = null;
    }
    isConnectingRef.current = false;
  }, [channelName]);

  const connect = useCallback(async () => {
    if (isConnectingRef.current || channelRef.current) {
      return channelRef.current;
    }

    try {
      isConnectingRef.current = true;
      setStatus('connecting');
      console.log(`Connecting to channel: ${channelName}`);

      const channel = supabase.channel(channelName);
      channelRef.current = channel;

      await new Promise<void>((resolve, reject) => {
        channel.subscribe((status, err) => {
          console.log(`Channel ${channelName} status:`, status);
          
          if (err) {
            console.error(`Channel ${channelName} error:`, err);
            setStatus('error');
            onError?.(err);
            if (showToasts) {
              toast.error('Connection error occurred');
            }
            reject(err);
            return;
          }

          switch (status) {
            case 'SUBSCRIBED':
              console.log(`Successfully connected to ${channelName}`);
              setStatus('connected');
              setReconnectAttempts(0);
              onConnect?.();
              if (showToasts && reconnectAttempts > 0) {
                toast.success('Reconnected successfully');
              }
              resolve();
              break;
            case 'CHANNEL_ERROR':
              console.error(`Channel ${channelName} error occurred`);
              setStatus('error');
              onError?.(new Error('Channel error'));
              reject(new Error('Channel error'));
              break;
            case 'TIMED_OUT':
              console.error(`Channel ${channelName} timed out`);
              setStatus('error');
              onError?.(new Error('Connection timed out'));
              reject(new Error('Connection timed out'));
              break;
            case 'CLOSED':
              console.log(`Channel ${channelName} closed`);
              setStatus('disconnected');
              onDisconnect?.();
              break;
          }
        });
      });

      return channel;
    } catch (error) {
      console.error(`Failed to connect to ${channelName}:`, error);
      setStatus('error');
      onError?.(error);
      
      // Auto-reconnect logic
      if (autoReconnect && reconnectAttempts < maxReconnectAttempts) {
        const nextAttempt = reconnectAttempts + 1;
        setReconnectAttempts(nextAttempt);
        
        console.log(`Scheduling reconnect attempt ${nextAttempt}/${maxReconnectAttempts} in ${reconnectDelay}ms`);
        
        reconnectTimeoutRef.current = setTimeout(() => {
          connect();
        }, reconnectDelay);
      } else if (reconnectAttempts >= maxReconnectAttempts) {
        console.error(`Max reconnect attempts (${maxReconnectAttempts}) reached for ${channelName}`);
        if (showToasts) {
          toast.error('Connection failed after multiple attempts');
        }
      }
      
      throw error;
    } finally {
      isConnectingRef.current = false;
    }
  }, [
    channelName,
    onConnect,
    onDisconnect,
    onError,
    autoReconnect,
    maxReconnectAttempts,
    reconnectDelay,
    reconnectAttempts,
    showToasts
  ]);

  const disconnect = useCallback(() => {
    console.log(`Disconnecting from channel: ${channelName}`);
    cleanup();
    setStatus('disconnected');
    setReconnectAttempts(0);
  }, [channelName, cleanup]);

  const reconnect = useCallback(() => {
    console.log(`Manual reconnect requested for: ${channelName}`);
    disconnect();
    setReconnectAttempts(0);
    connect();
  }, [channelName, disconnect, connect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return {
    status,
    reconnectAttempts,
    maxReconnectAttempts,
    channel: channelRef.current,
    connect,
    disconnect,
    reconnect,
    isConnected: status === 'connected',
    isConnecting: status === 'connecting',
    hasError: status === 'error'
  };
};

export default useRealtimeConnection;
