
import { useState, useRef, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Upload,
  Link,
  Maximize,
  SkipBack,
  SkipForward,
  Crown,
  Users
} from "lucide-react";
import { extractYouTubeVideoId, isYouTubeUrl, getYouTubeEmbedUrl } from "@/utils/youtube";
import { useRoomVideoState } from "@/hooks/useRoomVideoState";
import { getSupabaseClient, supabase } from "@/lib/supabase";
import { toast } from "sonner";
import { useAuth } from "@clerk/clerk-react";
import { ensureVideosBucket } from "@/utils/storage-setup";
import { validateVideoFile, formatFileSize, getEstimatedUploadTime } from "@/utils/file-validation";

interface VideoPlayerProps {
  roomId: string;
  isMuted: boolean;
  onMuteToggle: () => void;
  isOwner: boolean;
  onPlayingStateChange?: (isPlaying: boolean) => void;
}

const VideoPlayer = ({ roomId, isMuted, onMuteToggle, isOwner, onPlayingStateChange }: VideoPlayerProps) => {
  const {
    videoState,
    loading,
    setVideoUrl,
    setPlaying,
    setCurrentTime,
    setDuration,
    setPlaybackRate,
    connectionStatus
  } = useRoomVideoState(roomId, isOwner);
  const { getToken, isSignedIn } = useAuth();
  const [inputUrl, setInputUrl] = useState("");
  const [showUrlInput, setShowUrlInput] = useState(false);
  const [volume, setVolume] = useState(1);
  const [uploadingFile, setUploadingFile] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const lastUpdateRef = useRef<number>(0);

  // Derived state from videoState
  const hasVideo = !!videoState?.video_url;
  const isPlaying = videoState?.is_playing || false;
  const currentTime = videoState?.video_current_time || 0;
  const duration = videoState?.video_duration || 0;
  const isYouTubeVideo = videoState?.video_type === 'youtube';
  const youTubeVideoId = videoState?.youtube_video_id;

  // Notify parent component of playing state changes
  useEffect(() => {
    onPlayingStateChange?.(isPlaying);
  }, [isPlaying, onPlayingStateChange]);

  // Sync video state for non-owners with debouncing
  useEffect(() => {
    if (!isOwner && !isYouTubeVideo && videoRef.current && hasVideo) {
      const now = Date.now();
      if (now - lastUpdateRef.current < 100) return; // Debounce updates

      const video = videoRef.current;
      const timeDiff = Math.abs(video.currentTime - currentTime);
      const playStateDiff = video.paused !== !isPlaying;

      if (timeDiff > 1 || playStateDiff) {
        setSyncing(true);
        console.log('Syncing video state:', { currentTime, isPlaying, timeDiff, playStateDiff });

        // Sync time
        if (timeDiff > 1) {
          video.currentTime = currentTime;
        }

        // Sync play state
        if (isPlaying && video.paused) {
          video.play().catch(error => {
            console.error('Error playing video:', error);
          });
        } else if (!isPlaying && !video.paused) {
          video.pause();
        }

        lastUpdateRef.current = now;
        setTimeout(() => setSyncing(false), 200);
      }
    }
  }, [currentTime, isPlaying, isOwner, isYouTubeVideo, hasVideo]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !isOwner) {
      return;
    }

    const validation = validateVideoFile(file);
    if (!validation.isValid) {
      toast.error(validation.error || 'Invalid file selected');
      if (event.target) {
        event.target.value = '';
      }
      return;
    }

    if (validation.warnings) {
      validation.warnings.forEach(warning => {
        toast.warning(warning);
      });
    }

    if (file.size > 52428800) {
      toast.info(`Uploading ${formatFileSize(file.size)} - estimated time: ${getEstimatedUploadTime(file.size)}`);
    }

    setUploadingFile(true);

    try {
      await ensureVideosBucket();

      // Use anonymous client for uploads to avoid JWT/UUID issues
      // We've made the upload policy permissive temporarily
      const client = supabase;

      const fileExt = file.name.split('.').pop();
      const fileName = `${roomId}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;

      console.log(`Attempting to upload file: ${fileName}, size: ${formatFileSize(file.size)}, type: ${file.type}`);

      const { data, error } = await client.storage
        .from('videos')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Upload error details:', error);
        
        if (error.message?.includes('exceeded the maximum allowed size')) {
          throw new Error(`File size exceeds the maximum limit of 500MB. Your file is ${formatFileSize(file.size)}.`);
        } else if (error.message?.includes('Invalid MIME type')) {
          throw new Error('Unsupported video format. Please use MP4, WebM, or other supported formats.');
        } else if (error.message?.includes('Bucket not found')) {
          throw new Error('Video storage is not properly configured. Please contact support.');
        } else if (error.message?.includes('Insufficient permissions')) {
          throw new Error('You do not have permission to upload videos to this room.');
        } else {
          throw new Error(`Upload failed: ${error.message}`);
        }
      }

      console.log('Upload successful:', data);

      const { data: { publicUrl } } = client.storage
        .from('videos')
        .getPublicUrl(fileName);

      setVideoUrl(publicUrl, 'file');
      toast.success(`Video uploaded successfully! (${formatFileSize(file.size)})`);

    } catch (error) {
      console.error('Error uploading video:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload video. Please try again.';
      toast.error(errorMessage);
    } finally {
      setUploadingFile(false);
      if (event.target) {
        event.target.value = '';
      }
    }
  };

  const handleUrlSubmit = () => {
    if (inputUrl.trim() && isOwner) {
      const trimmedUrl = inputUrl.trim();

      if (isYouTubeUrl(trimmedUrl)) {
        const videoId = extractYouTubeVideoId(trimmedUrl);
        if (videoId) {
          console.log('Setting YouTube video:', { url: trimmedUrl, videoId });
          setVideoUrl(trimmedUrl, 'youtube', videoId);
          setShowUrlInput(false);
          setInputUrl("");
          toast.success('YouTube video added successfully');
          return;
        } else {
          toast.error('Invalid YouTube URL. Please check the URL and try again.');
          return;
        }
      }

      try {
        new URL(trimmedUrl);
        setVideoUrl(trimmedUrl, 'url');
        setShowUrlInput(false);
        setInputUrl("");
        toast.success('Video URL added successfully');
      } catch (error) {
        toast.error('Invalid URL format. Please enter a valid video URL.');
      }
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current && isOwner) {
      const now = Date.now();
      if (now - lastUpdateRef.current > 1000) { // Only update every second
        setCurrentTime(videoRef.current.currentTime);
        lastUpdateRef.current = now;
      }
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current && isOwner) {
      setDuration(videoRef.current.duration);
      console.log('Video metadata loaded, duration:', videoRef.current.duration);
    }
  };

  const handleCanPlay = () => {
    if (videoRef.current && !isOwner) {
      // For non-owners, sync immediately when video can play
      console.log('Video can play, syncing state');
      const video = videoRef.current;
      video.currentTime = currentTime;
      if (isPlaying) {
        video.play().catch(error => {
          console.error('Error auto-playing video:', error);
        });
      }
    }
  };

  const handleSeek = (time: number) => {
    if (isOwner) {
      setCurrentTime(time);
      if (videoRef.current) {
        videoRef.current.currentTime = time;
      }
    }
  };

  const handlePlayPause = () => {
    if (isOwner && videoRef.current) {
      const newPlayingState = !isPlaying;
      setPlaying(newPlayingState, videoRef.current.currentTime);

      if (newPlayingState) {
        videoRef.current.play().catch(error => {
          console.error('Error playing video:', error);
        });
      } else {
        videoRef.current.pause();
      }
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume);
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
    }
  };

  if (!hasVideo) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-slate-900">
        <Card className="w-full max-w-md mx-4 bg-slate-800 border-slate-700">
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-6">
              <Play className="w-8 h-8 text-slate-300" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">No Video Loaded</h3>
            <p className="text-slate-400 mb-6">Upload a video file or add a YouTube URL to start watching together.</p>
            
            {isOwner ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={uploadingFile}
                    className="w-full bg-slate-700 hover:bg-slate-600 text-white disabled:opacity-50"
                  >
                    {uploadingFile ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Uploading...
                      </>
                    ) : (
                      <>
                        <Upload className="w-4 h-4 mr-2" />
                        Upload Video File
                      </>
                    )}
                  </Button>
                  <p className="text-xs text-slate-400 text-center">
                    Max file size: 500MB • Supported: MP4, WebM, OGG, AVI, MOV
                  </p>
                </div>
                
                <Button 
                  onClick={() => setShowUrlInput(!showUrlInput)}
                  variant="outline"
                  className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
                >
                  <Link className="w-4 h-4 mr-2" />
                  Add Video URL
                </Button>

                {showUrlInput && (
                  <div className="flex gap-2 mt-4">
                    <Input
                      placeholder="Enter YouTube URL or video URL..."
                      value={inputUrl}
                      onChange={(e) => setInputUrl(e.target.value)}
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                    />
                    <Button onClick={handleUrlSubmit} size="sm">
                      Add
                    </Button>
                  </div>
                )}

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="video/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>
            ) : (
              <p className="text-slate-500 text-sm">Only the room owner can add videos.</p>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full bg-black group">
      {isYouTubeVideo && youTubeVideoId ? (
        <div className="relative w-full h-full">
          <iframe
            src={getYouTubeEmbedUrl(youTubeVideoId)}
            className="w-full h-full"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            title="YouTube Video Player"
          />
          
          {/* Control Status Overlay */}
          <div className="absolute top-2 right-2 flex gap-2">
            <Badge variant={isOwner ? "default" : "secondary"} className="bg-black bg-opacity-70">
              {isOwner ? (
                <>
                  <Crown className="w-3 h-3 mr-1" />
                  Owner (Controls)
                </>
              ) : (
                <>
                  <Users className="w-3 h-3 mr-1" />
                  Viewer
                </>
              )}
            </Badge>
            <Badge variant="outline" className={`text-white border-white ${
              connectionStatus === 'connected' ? 'bg-green-600' :
              connectionStatus === 'connecting' ? 'bg-yellow-600' : 'bg-red-600'
            }`}>
              {connectionStatus === 'connected' ? 'Synced' :
               connectionStatus === 'connecting' ? 'Connecting' : 'Offline'}
            </Badge>
          </div>

          {!isOwner && (
            <div className="absolute bottom-2 left-2 bg-amber-600 bg-opacity-90 text-white text-xs px-2 py-1 rounded">
              <span>Only the room owner can control playback</span>
            </div>
          )}
        </div>
      ) : (
        <>
          <video
            ref={videoRef}
            src={videoState?.video_url}
            className="w-full h-full object-contain"
            onTimeUpdate={handleTimeUpdate}
            onLoadedMetadata={handleLoadedMetadata}
            onCanPlay={handleCanPlay}
            controls={false}
            muted={isMuted}
            volume={volume}
          />

          {/* Control Status Overlay */}
          <div className="absolute top-2 right-2 flex gap-2">
            <Badge variant={isOwner ? "default" : "secondary"} className="bg-black bg-opacity-70">
              {isOwner ? (
                <>
                  <Crown className="w-3 h-3 mr-1" />
                  Owner (Controls)
                </>
              ) : (
                <>
                  <Users className="w-3 h-3 mr-1" />
                  Viewer
                </>
              )}
            </Badge>
            <Badge variant="outline" className={`text-white border-white ${
              connectionStatus === 'connected' ? 'bg-green-600' :
              connectionStatus === 'connecting' ? 'bg-yellow-600' : 'bg-red-600'
            }`}>
              {connectionStatus === 'connected' ? 'Synced' :
               connectionStatus === 'connecting' ? 'Connecting' : 'Offline'}
            </Badge>
            {syncing && (
              <Badge variant="outline" className="bg-blue-600 bg-opacity-70 text-white border-blue-400">
                Syncing...
              </Badge>
            )}
          </div>

          {!isOwner && (
            <div className="absolute bottom-2 left-2 bg-amber-600 bg-opacity-90 text-white text-xs px-2 py-1 rounded">
              <span>Only the room owner can control playback</span>
            </div>
          )}

          {/* Video Controls Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div className="absolute bottom-0 left-0 right-0 p-6">
              {/* Progress Bar */}
              <div className="mb-4">
                <div className="flex items-center gap-2 text-white text-sm mb-2">
                  <span>{formatTime(currentTime)}</span>
                  <span>/</span>
                  <span>{formatTime(duration)}</span>
                </div>
                <div 
                  className="w-full h-2 bg-slate-700 rounded-full cursor-pointer"
                  onClick={(e) => {
                    if (isOwner && duration > 0) {
                      const rect = e.currentTarget.getBoundingClientRect();
                      const percent = (e.clientX - rect.left) / rect.width;
                      handleSeek(percent * duration);
                    }
                  }}
                >
                  <div 
                    className="h-full bg-white rounded-full transition-all duration-150"
                    style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                  />
                </div>
              </div>

              {/* Control Buttons */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  {isOwner && (
                    <>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSeek(Math.max(0, currentTime - 10))}
                        className="text-white hover:bg-white/20"
                      >
                        <SkipBack className="w-5 h-5" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handlePlayPause}
                        className="text-white hover:bg-white/20"
                      >
                        {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSeek(Math.min(duration, currentTime + 10))}
                        className="text-white hover:bg-white/20"
                      >
                        <SkipForward className="w-5 h-5" />
                      </Button>
                    </>
                  )}
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onMuteToggle}
                      className="text-white hover:bg-white/20"
                    >
                      {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                    </Button>
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={isMuted ? 0 : volume}
                      onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                      className="w-20 h-1 bg-slate-600 rounded-lg appearance-none cursor-pointer"
                    />
                  </div>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    videoRef.current?.requestFullscreen();
                  }}
                  className="text-white hover:bg-white/20"
                >
                  <Maximize className="w-5 h-5" />
                </Button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default VideoPlayer;
