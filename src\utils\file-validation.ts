// File validation utilities for video uploads

export const MAX_VIDEO_SIZE = 524288000; // 500MB in bytes
export const SUPPORTED_VIDEO_TYPES = [
  'video/mp4',
  'video/webm', 
  'video/ogg',
  'video/avi',
  'video/mov',
  'video/wmv',
  'video/flv',
  'video/mkv'
];

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
}

export const validateVideoFile = (file: File): FileValidationResult => {
  const warnings: string[] = [];
  
  // Check if file is a video
  if (!file.type.startsWith('video/')) {
    return {
      isValid: false,
      error: 'Please select a valid video file.'
    };
  }
  
  // Check file size
  if (file.size > MAX_VIDEO_SIZE) {
    return {
      isValid: false,
      error: `File size (${formatFileSize(file.size)}) exceeds the maximum limit of ${formatFileSize(MAX_VIDEO_SIZE)}.`
    };
  }
  
  // Check if file type is supported
  if (!SUPPORTED_VIDEO_TYPES.includes(file.type)) {
    warnings.push(`File type ${file.type} may not be supported. Recommended formats: MP4, WebM, OGG.`);
  }
  
  // Warn about large files
  if (file.size > 104857600) { // 100MB
    warnings.push(`Large file detected (${formatFileSize(file.size)}). Upload may take some time.`);
  }
  
  return {
    isValid: true,
    warnings: warnings.length > 0 ? warnings : undefined
  };
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const getEstimatedUploadTime = (fileSize: number): string => {
  // Rough estimation based on average upload speeds
  // Assuming 1MB/s average upload speed
  const seconds = fileSize / (1024 * 1024);
  
  if (seconds < 60) {
    return `~${Math.ceil(seconds)} seconds`;
  } else if (seconds < 3600) {
    return `~${Math.ceil(seconds / 60)} minutes`;
  } else {
    return `~${Math.ceil(seconds / 3600)} hours`;
  }
};
