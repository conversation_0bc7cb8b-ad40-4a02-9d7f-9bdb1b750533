-- Create rooms table
CREATE TABLE IF NOT EXISTS rooms (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VA<PERSON>HAR(255) NOT NULL,
  description TEXT,
  code VARCHAR(10) UNIQUE NOT NULL,
  owner_id VARCHAR(255) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create room_members table
CREATE TABLE IF NOT EXISTS room_members (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID REFERENCES rooms(id) ON DELETE CASCADE,
  user_id VARCHAR(255) NOT NULL,
  role VARCHAR(20) CHECK (role IN ('owner', 'member')) DEFAULT 'member',
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(room_id, user_id)
);

-- Create room_invitations table
CREATE TABLE IF NOT EXISTS room_invitations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID REFERENCES rooms(id) ON DELETE CASCADE,
  email VARCHAR(255) NOT NULL,
  invited_by VARCHAR(255) NOT NULL,
  status VARCHAR(20) CHECK (status IN ('pending', 'accepted', 'declined')) DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days')
);

-- Create room_messages table for chat
CREATE TABLE IF NOT EXISTS room_messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID REFERENCES rooms(id) ON DELETE CASCADE,
  user_id VARCHAR(255) NOT NULL,
  user_name VARCHAR(255) NOT NULL,
  user_image VARCHAR(500),
  content TEXT NOT NULL,
  message_type VARCHAR(20) CHECK (message_type IN ('message', 'system')) DEFAULT 'message',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create room_video_state table for video synchronization
CREATE TABLE IF NOT EXISTS room_video_state (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID REFERENCES rooms(id) ON DELETE CASCADE,
  video_url TEXT,
  video_type VARCHAR(20) CHECK (video_type IN ('youtube', 'file', 'url')) DEFAULT 'url',
  youtube_video_id VARCHAR(50),
  is_playing BOOLEAN DEFAULT FALSE,
  video_current_time DECIMAL(12,6) DEFAULT 0, -- Increased precision for microsecond accuracy
  video_duration DECIMAL(12,6) DEFAULT 0,
  playback_rate DECIMAL(3,2) DEFAULT 1.0, -- Support for playback speed control
  last_sync_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- For precise sync timing
  sync_version INTEGER DEFAULT 1, -- Version control for sync conflicts
  updated_by VARCHAR(255) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(room_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_rooms_code ON rooms(code);
CREATE INDEX IF NOT EXISTS idx_rooms_owner_id ON rooms(owner_id);
CREATE INDEX IF NOT EXISTS idx_room_members_room_id ON room_members(room_id);
CREATE INDEX IF NOT EXISTS idx_room_members_user_id ON room_members(user_id);
CREATE INDEX IF NOT EXISTS idx_room_invitations_room_id ON room_invitations(room_id);
CREATE INDEX IF NOT EXISTS idx_room_invitations_email ON room_invitations(email);
CREATE INDEX IF NOT EXISTS idx_room_messages_room_id ON room_messages(room_id);
CREATE INDEX IF NOT EXISTS idx_room_messages_created_at ON room_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_room_video_state_room_id ON room_video_state(room_id);

-- Enable Row Level Security (RLS)
ALTER TABLE rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_video_state ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Rooms policies
CREATE POLICY "Users can view rooms they are members of" ON rooms
  FOR SELECT USING (
    id IN (
      SELECT room_id FROM room_members 
      WHERE user_id = current_setting('request.jwt.claims', true)::json->>'sub'
    )
  );

CREATE POLICY "Users can create rooms" ON rooms
  FOR INSERT WITH CHECK (
    owner_id = current_setting('request.jwt.claims', true)::json->>'sub'
  );

CREATE POLICY "Room owners can update their rooms" ON rooms
  FOR UPDATE USING (
    owner_id = current_setting('request.jwt.claims', true)::json->>'sub'
  );

CREATE POLICY "Room owners can delete their rooms" ON rooms
  FOR DELETE USING (
    owner_id = current_setting('request.jwt.claims', true)::json->>'sub'
  );

-- Room members policies
CREATE POLICY "Users can view room members for rooms they belong to" ON room_members
  FOR SELECT USING (
    room_id IN (
      SELECT room_id FROM room_members 
      WHERE user_id = current_setting('request.jwt.claims', true)::json->>'sub'
    )
  );

CREATE POLICY "Users can join rooms" ON room_members
  FOR INSERT WITH CHECK (
    user_id = current_setting('request.jwt.claims', true)::json->>'sub'
  );

CREATE POLICY "Room owners can manage members" ON room_members
  FOR ALL USING (
    room_id IN (
      SELECT id FROM rooms 
      WHERE owner_id = current_setting('request.jwt.claims', true)::json->>'sub'
    )
  );

CREATE POLICY "Users can leave rooms" ON room_members
  FOR DELETE USING (
    user_id = current_setting('request.jwt.claims', true)::json->>'sub'
  );

-- Room messages policies
CREATE POLICY "Users can view messages for rooms they belong to" ON room_messages
  FOR SELECT USING (
    room_id IN (
      SELECT room_id FROM room_members
      WHERE user_id = current_setting('request.jwt.claims', true)::json->>'sub'
    )
  );

CREATE POLICY "Users can send messages to rooms they belong to" ON room_messages
  FOR INSERT WITH CHECK (
    user_id = current_setting('request.jwt.claims', true)::json->>'sub' AND
    room_id IN (
      SELECT room_id FROM room_members
      WHERE user_id = current_setting('request.jwt.claims', true)::json->>'sub'
    )
  );

-- Room video state policies
CREATE POLICY "Users can view video state for rooms they belong to" ON room_video_state
  FOR SELECT USING (
    room_id IN (
      SELECT room_id FROM room_members
      WHERE user_id = current_setting('request.jwt.claims', true)::json->>'sub'
    )
  );

CREATE POLICY "Room owners can update video state" ON room_video_state
  FOR ALL USING (
    room_id IN (
      SELECT id FROM rooms
      WHERE owner_id = current_setting('request.jwt.claims', true)::json->>'sub'
    )
  );

-- Room invitations policies
CREATE POLICY "Users can view invitations for rooms they own" ON room_invitations
  FOR SELECT USING (
    room_id IN (
      SELECT id FROM rooms 
      WHERE owner_id = current_setting('request.jwt.claims', true)::json->>'sub'
    )
  );

CREATE POLICY "Room owners can create invitations" ON room_invitations
  FOR INSERT WITH CHECK (
    room_id IN (
      SELECT id FROM rooms 
      WHERE owner_id = current_setting('request.jwt.claims', true)::json->>'sub'
    )
  );

CREATE POLICY "Room owners can update invitations" ON room_invitations
  FOR UPDATE USING (
    room_id IN (
      SELECT id FROM rooms 
      WHERE owner_id = current_setting('request.jwt.claims', true)::json->>'sub'
    )
  );

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for rooms table
CREATE TRIGGER update_rooms_updated_at 
  BEFORE UPDATE ON rooms 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Create function to ensure room code uniqueness
CREATE OR REPLACE FUNCTION generate_unique_room_code()
RETURNS TRIGGER AS $$
DECLARE
  new_code VARCHAR(10);
  code_exists BOOLEAN;
BEGIN
  LOOP
    -- Generate a random 6-character code
    new_code := upper(substring(md5(random()::text) from 1 for 6));
    
    -- Check if code already exists
    SELECT EXISTS(SELECT 1 FROM rooms WHERE code = new_code) INTO code_exists;
    
    -- If code doesn't exist, use it
    IF NOT code_exists THEN
      NEW.code := new_code;
      EXIT;
    END IF;
  END LOOP;
  
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to auto-generate room codes if not provided
CREATE TRIGGER generate_room_code_trigger
  BEFORE INSERT ON rooms
  FOR EACH ROW
  WHEN (NEW.code IS NULL OR NEW.code = '')
  EXECUTE FUNCTION generate_unique_room_code();
