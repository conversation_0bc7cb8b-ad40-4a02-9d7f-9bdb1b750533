-- Create storage bucket for videos
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'videos',
  'videos',
  true,
  524288000, -- 500MB limit
  ARRAY['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/mkv']
)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for videos bucket
CREATE POLICY "Users can upload videos to their rooms" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'videos' AND
    auth.uid()::text = (storage.foldername(name))[1] OR
    auth.uid()::text IN (
      SELECT user_id FROM room_members 
      WHERE room_id::text = (storage.foldername(name))[1] 
      AND role = 'owner'
    )
  );

CREATE POLICY "Users can view videos in rooms they belong to" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'videos' AND
    (storage.foldername(name))[1] IN (
      SELECT room_id::text FROM room_members
      WHERE user_id = auth.uid()::text
    )
  );

CREATE POLICY "Room owners can delete videos" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'videos' AND
    auth.uid()::text IN (
      SELECT user_id FROM room_members 
      WHERE room_id::text = (storage.foldername(name))[1] 
      AND role = 'owner'
    )
  );

-- Grant necessary permissions for storage
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.buckets TO authenticated;
