import { useState } from 'react';
import { useAuth, useUser } from '@clerk/clerk-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { createAuthenticatedSupabaseClient } from '@/lib/supabase';
import { toast } from 'sonner';

export const SupabaseTest = () => {
  const { getToken, isSignedIn } = useAuth();
  const { user } = useUser();
  const [testResults, setTestResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const runSupabaseTests = async () => {
    setLoading(true);
    setTestResults(null);
    
    const results = {
      tokenTest: { success: false, message: '', data: null },
      connectionTest: { success: false, message: '', data: null },
      jwtClaimsTest: { success: false, message: '', data: null },
      roomsTest: { success: false, message: '', data: null }
    };

    try {
      // Test 1: Get JWT Token
      console.log('Testing JWT token...');
      const token = await getToken({ template: 'supabase' });
      
      if (!token) {
        results.tokenTest = { 
          success: false, 
          message: 'No JWT token received. Check Clerk JWT template configuration.',
          data: null 
        };
      } else {
        results.tokenTest = { 
          success: true, 
          message: 'JWT token received successfully',
          data: { tokenLength: token.length, tokenPreview: token.substring(0, 50) + '...' }
        };

        // Test 2: Create authenticated Supabase client
        console.log('Testing Supabase client...');
        const client = createAuthenticatedSupabaseClient(token);
        
        // Test 3: Test JWT claims function
        console.log('Testing JWT claims...');
        try {
          const { data: claimsData, error: claimsError } = await client
            .rpc('debug_jwt_claims');
          
          if (claimsError) {
            results.jwtClaimsTest = {
              success: false,
              message: `JWT claims test failed: ${claimsError.message}`,
              data: claimsError
            };
          } else {
            results.jwtClaimsTest = {
              success: true,
              message: 'JWT claims accessible',
              data: claimsData
            };
          }
        } catch (err: any) {
          results.jwtClaimsTest = {
            success: false,
            message: `JWT claims function not available: ${err.message}`,
            data: err
          };
        }

        // Test 4: Test basic Supabase connection
        console.log('Testing Supabase connection...');
        const { data: connectionData, error: connectionError } = await client
          .from('rooms')
          .select('id')
          .limit(1);

        if (connectionError) {
          results.connectionTest = {
            success: false,
            message: `Connection failed: ${connectionError.message}`,
            data: connectionError
          };
        } else {
          results.connectionTest = {
            success: true,
            message: 'Successfully connected to Supabase',
            data: connectionData
          };
        }

        // Test 5: Test rooms access
        console.log('Testing rooms access...');
        const { data: roomsData, error: roomsError } = await client
          .from('rooms')
          .select('*')
          .limit(5);

        if (roomsError) {
          results.roomsTest = {
            success: false,
            message: `Rooms access failed: ${roomsError.message}`,
            data: roomsError
          };
        } else {
          results.roomsTest = {
            success: true,
            message: `Successfully accessed rooms (${roomsData?.length || 0} found)`,
            data: roomsData
          };
        }
      }

    } catch (error: any) {
      console.error('Test error:', error);
      toast.error(`Test failed: ${error.message}`);
    } finally {
      setLoading(false);
      setTestResults(results);
    }
  };

  if (!isSignedIn) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Supabase Connection Test</CardTitle>
          <CardDescription>Please sign in to test the connection</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Supabase Connection Test</CardTitle>
        <CardDescription>
          Test your authenticated connection to Supabase
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-4">
          <Button onClick={runSupabaseTests} disabled={loading}>
            {loading ? 'Running Tests...' : 'Run Connection Tests'}
          </Button>
          <div className="text-sm text-gray-600">
            User: {user?.primaryEmailAddress?.emailAddress}
          </div>
        </div>

        {testResults && (
          <div className="space-y-3">
            <h4 className="font-semibold">Test Results:</h4>
            
            {Object.entries(testResults).map(([testName, result]: [string, any]) => (
              <div key={testName} className="border rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant={result.success ? 'default' : 'destructive'}>
                    {result.success ? '✓' : '✗'}
                  </Badge>
                  <span className="font-medium capitalize">
                    {testName.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{result.message}</p>
                {result.data && (
                  <details className="text-xs">
                    <summary className="cursor-pointer text-gray-500">View Details</summary>
                    <pre className="mt-2 p-2 bg-gray-50 rounded overflow-auto">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}

            {/* Overall Status */}
            <div className="mt-4 p-3 rounded-lg bg-gray-50">
              <h5 className="font-medium mb-2">Overall Status:</h5>
              {testResults.tokenTest.success && testResults.connectionTest.success ? (
                <div className="text-green-700">
                  ✅ Authentication is working! You should be able to use room features.
                </div>
              ) : (
                <div className="text-red-700">
                  ❌ Authentication issues detected. Please check the setup guide.
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
