import { useState, useEffect, useRef, useCallback } from 'react';
import { useUser } from '@clerk/clerk-react';
import { toast } from 'sonner';

// Jitsi Meet API types
declare global {
  interface Window {
    JitsiMeetExternalAPI: any;
  }
}

export interface JitsiMeetConfig {
  roomName: string;
  domain?: string;
  width?: number | string;
  height?: number | string;
  parentNode?: HTMLElement;
  configOverwrite?: any;
  interfaceConfigOverwrite?: any;
  userInfo?: {
    displayName?: string;
    email?: string;
  };
}

export interface JitsiMeetAPI {
  executeCommand: (command: string, ...args: any[]) => void;
  addEventListener: (event: string, listener: (...args: any[]) => void) => void;
  removeEventListener: (event: string, listener: (...args: any[]) => void) => void;
  dispose: () => void;
  getParticipantsInfo: () => any[];
  isAudioMuted: () => Promise<boolean>;
  isVideoMuted: () => Promise<boolean>;
  getVideoQuality: () => number;
}

export const useJitsiMeet = () => {
  const { user } = useUser();
  const [jitsiAPI, setJitsiAPI] = useState<JitsiMeetAPI | null>(null);
  const [isJitsiLoaded, setIsJitsiLoaded] = useState(false);
  const [isJitsiConnected, setIsJitsiConnected] = useState(false);
  const [participants, setParticipants] = useState<any[]>([]);
  const [isAudioMuted, setIsAudioMuted] = useState(false);
  const [isVideoMuted, setIsVideoMuted] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const apiRef = useRef<JitsiMeetAPI | null>(null);

  // Load Jitsi Meet External API script
  const loadJitsiScript = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      if (window.JitsiMeetExternalAPI) {
        setIsJitsiLoaded(true);
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://meet.jit.si/external_api.js';
      script.async = true;
      
      script.onload = () => {
        console.log('Jitsi Meet External API loaded successfully');
        setIsJitsiLoaded(true);
        resolve();
      };

      script.onerror = () => {
        const error = 'Failed to load Jitsi Meet External API';
        console.error(error);
        setConnectionError(error);
        reject(new Error(error));
      };

      document.head.appendChild(script);
    });
  }, []);

  // Initialize Jitsi Meet
  const initializeJitsi = useCallback(async (config: JitsiMeetConfig) => {
    try {
      if (!isJitsiLoaded) {
        await loadJitsiScript();
      }

      if (!window.JitsiMeetExternalAPI) {
        throw new Error('Jitsi Meet External API not available');
      }

      // Default configuration
      const defaultConfig = {
        domain: 'meet.jit.si',
        width: '100%',
        height: '100%',
        configOverwrite: {
          startWithAudioMuted: true,
          startWithVideoMuted: false,
          enableWelcomePage: false,
          enableClosePage: false,
          prejoinPageEnabled: false,
          disableInviteFunctions: true,
          doNotStoreRoom: true,
          disableDeepLinking: true,
          disableShortcuts: true,
          disableLocalVideoFlip: false,
          remoteVideoMenu: {
            disableKick: true,
            disableGrantModerator: true,
            disablePrivateChat: false,
          },
          toolbarButtons: [
            'microphone',
            'camera',
            'chat',
            'participants-pane',
            'tileview',
            'fullscreen',
            'hangup'
          ],
        },
        interfaceConfigOverwrite: {
          DISABLE_JOIN_LEAVE_NOTIFICATIONS: true,
          DISABLE_PRESENCE_STATUS: true,
          HIDE_INVITE_MORE_HEADER: true,
          SHOW_JITSI_WATERMARK: false,
          SHOW_WATERMARK_FOR_GUESTS: false,
          SHOW_BRAND_WATERMARK: false,
          BRAND_WATERMARK_LINK: '',
          SHOW_POWERED_BY: false,
          DISPLAY_WELCOME_PAGE_CONTENT: false,
          DISPLAY_WELCOME_PAGE_TOOLBAR_ADDITIONAL_CONTENT: false,
          SHOW_CHROME_EXTENSION_BANNER: false,
          MOBILE_APP_PROMO: false,
          TILE_VIEW_MAX_COLUMNS: 4,
        },
        userInfo: {
          displayName: user?.fullName || user?.firstName || 'Anonymous',
          email: user?.primaryEmailAddress?.emailAddress || '',
        },
      };

      const finalConfig = {
        ...defaultConfig,
        ...config,
        configOverwrite: {
          ...defaultConfig.configOverwrite,
          ...config.configOverwrite,
        },
        interfaceConfigOverwrite: {
          ...defaultConfig.interfaceConfigOverwrite,
          ...config.interfaceConfigOverwrite,
        },
        userInfo: {
          ...defaultConfig.userInfo,
          ...config.userInfo,
        },
      };

      console.log('Initializing Jitsi Meet with config:', finalConfig);

      const api = new window.JitsiMeetExternalAPI(
        finalConfig.domain,
        finalConfig
      );

      // Set up event listeners
      api.addEventListener('videoConferenceJoined', () => {
        console.log('Jitsi: Conference joined');
        setIsJitsiConnected(true);
        setConnectionError(null);
        toast.success('Connected to voice/video chat');
      });

      api.addEventListener('videoConferenceLeft', () => {
        console.log('Jitsi: Conference left');
        setIsJitsiConnected(false);
        setParticipants([]);
      });

      api.addEventListener('participantJoined', (participant: any) => {
        console.log('Jitsi: Participant joined:', participant);
        setParticipants(prev => [...prev, participant]);
      });

      api.addEventListener('participantLeft', (participant: any) => {
        console.log('Jitsi: Participant left:', participant);
        setParticipants(prev => prev.filter(p => p.id !== participant.id));
      });

      api.addEventListener('audioMuteStatusChanged', (event: any) => {
        console.log('Jitsi: Audio mute status changed:', event);
        setIsAudioMuted(event.muted);
      });

      api.addEventListener('videoMuteStatusChanged', (event: any) => {
        console.log('Jitsi: Video mute status changed:', event);
        setIsVideoMuted(event.muted);
      });

      api.addEventListener('readyToClose', () => {
        console.log('Jitsi: Ready to close');
        setIsJitsiConnected(false);
        setParticipants([]);
      });

      api.addEventListener('participantRoleChanged', (event: any) => {
        console.log('Jitsi: Participant role changed:', event);
      });

      apiRef.current = api;
      setJitsiAPI(api);

      return api;
    } catch (error) {
      console.error('Error initializing Jitsi Meet:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize Jitsi Meet';
      setConnectionError(errorMessage);
      toast.error(errorMessage);
      throw error;
    }
  }, [isJitsiLoaded, loadJitsiScript, user]);

  // Cleanup Jitsi Meet
  const disposeJitsi = useCallback(() => {
    if (apiRef.current) {
      try {
        apiRef.current.dispose();
      } catch (error) {
        console.error('Error disposing Jitsi Meet:', error);
      }
      apiRef.current = null;
      setJitsiAPI(null);
      setIsJitsiConnected(false);
      setParticipants([]);
      setIsAudioMuted(false);
      setIsVideoMuted(false);
      setConnectionError(null);
    }
  }, []);

  // Jitsi commands
  const toggleAudio = useCallback(() => {
    if (jitsiAPI) {
      jitsiAPI.executeCommand('toggleAudio');
    }
  }, [jitsiAPI]);

  const toggleVideo = useCallback(() => {
    if (jitsiAPI) {
      jitsiAPI.executeCommand('toggleVideo');
    }
  }, [jitsiAPI]);

  const hangUp = useCallback(() => {
    if (jitsiAPI) {
      jitsiAPI.executeCommand('hangup');
    }
  }, [jitsiAPI]);

  const setDisplayName = useCallback((name: string) => {
    if (jitsiAPI) {
      jitsiAPI.executeCommand('displayName', name);
    }
  }, [jitsiAPI]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disposeJitsi();
    };
  }, [disposeJitsi]);

  return {
    // State
    jitsiAPI,
    isJitsiLoaded,
    isJitsiConnected,
    participants,
    isAudioMuted,
    isVideoMuted,
    connectionError,
    
    // Actions
    initializeJitsi,
    disposeJitsi,
    toggleAudio,
    toggleVideo,
    hangUp,
    setDisplayName,
    loadJitsiScript,
  };
};
