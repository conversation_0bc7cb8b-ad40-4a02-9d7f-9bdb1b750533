import { spawn } from 'child_process';

// Set environment variable for HTTPS
process.env.VITE_HTTPS = 'true';

// Start Vite with HTTPS
const vite = spawn('npx', ['vite', '--host', '::', '--port', '8080'], {
  stdio: 'inherit',
  shell: true,
  env: { ...process.env, VITE_HTTPS: 'true' }
});

vite.on('close', (code) => {
  console.log(`Vite process exited with code ${code}`);
});

vite.on('error', (err) => {
  console.error('Failed to start Vite:', err);
});

console.log('Starting Vite with HTTPS...');
