import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useUser } from "@clerk/clerk-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Users,
  MessageCircle,
  Settings,
  ArrowLeft,
  Copy,
  UserPlus,
  Crown,
  MoreVertical,
  Video,
  Mic,
  MicOff,
  Phone,
  Minimize2,
  Maximize2
} from "lucide-react";
import { toast } from "sonner";
import UserProfileButton from "@/components/UserProfileButton";
import RoomMembersList from "@/components/RoomMembersList";
import RoomChat from "@/components/RoomChat";
import VideoPlayer from "@/components/VideoPlayer";
import RoomSettingsDialog from "@/components/RoomSettingsDialog";
import JitsiMeetRoom from "@/components/JitsiMeetRoom";
import ConnectionStatus from "@/components/ConnectionStatus";
import ConnectionDiagnostics from "@/components/ConnectionDiagnostics";
import { BackgroundRefreshIndicator } from "@/components/BackgroundRefreshIndicator";
import { useRoom } from "@/hooks/useRooms";
import { useRefreshTracker } from "@/hooks/useRefreshTracker";
import { useVideoSync } from "@/hooks/useVideoSync";

const RoomPage = () => {
  const { roomId } = useParams<{ roomId: string }>();
  const navigate = useNavigate();
  const { user } = useUser();
  const { room, loading: isLoaded, connectionStatus, refetch } = useRoom(roomId || "");
  const { isRefreshing } = useRefreshTracker();

  const [isMuted, setIsMuted] = useState(false);
  const [showMembers, setShowMembers] = useState(false);
  const [showChat, setShowChat] = useState(true);
  const [showVoiceVideo, setShowVoiceVideo] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [isJitsiMinimized, setIsJitsiMinimized] = useState(false);
  const [showDiagnostics, setShowDiagnostics] = useState(false);

  const isOwner = room?.user_role === "owner";

  // Initialize video sync for cinema experience
  const {
    cinemaSync,
    isConnected: isSyncConnected,
    playVideo,
    pauseVideo,
    seekVideo,
    changeVideo
  } = useVideoSync(roomId || "", isOwner || false);

  useEffect(() => {
    if (!isLoaded && !room && roomId) {
      toast.error("Room not found or you don't have access");
      navigate("/dashboard");
    }
  }, [isLoaded, room, navigate, roomId]);

  const handleCopyRoomCode = () => {
    if (room?.code) {
      navigator.clipboard.writeText(room.code);
      toast.success("Room code copied to clipboard!");
    }
  };

  const handleLeaveRoom = () => {
    navigate("/dashboard");
  };

  const handleRoomUpdated = () => {
    refetch(); // Refresh room data
  };

  const handleRoomDeleted = () => {
    navigate("/dashboard");
  };

  if (isLoaded || !room) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-slate-600 border-t-white rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-300">Loading room...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <header className="border-b border-slate-700 bg-slate-800/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={handleLeaveRoom}
                className="text-slate-300 hover:text-white hover:bg-slate-700"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <Separator orientation="vertical" className="h-6 bg-slate-600" />
              <div>
                <div className="flex items-center space-x-2">
                  <h1 className="text-xl font-bold text-white">{room.name}</h1>
                  {isOwner && <Crown className="w-4 h-4 text-amber-400" />}
                </div>
                <div className="flex items-center space-x-4 text-sm text-slate-400">
                  <span className="flex items-center">
                    <Users className="w-3 h-3 mr-1" />
                    {room.member_count} member{room.member_count !== 1 ? 's' : ''}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopyRoomCode}
                    className="text-slate-400 hover:text-white h-auto p-1"
                  >
                    <Copy className="w-3 h-3 mr-1" />
                    Copy Code
                  </Button>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <ConnectionStatus
                status={connectionStatus}
                showText={true}
                className="text-xs"
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowMembers(!showMembers)}
                className="text-slate-300 hover:text-white hover:bg-slate-700"
              >
                <Users className="w-4 h-4 mr-2" />
                Members
              </Button>
              <UserProfileButton />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* Video Section */}
        <div className="flex-1 flex flex-col">
          {/* Video Player */}
          <div className="flex-1 bg-black relative">
            <VideoPlayer
              roomId={roomId || ""}
              isMuted={isMuted}
              onMuteToggle={() => setIsMuted(!isMuted)}
              isOwner={isOwner || false}
              onPlayingStateChange={setIsPlaying}
            />
          </div>

          {/* Video Controls */}
          <div className="bg-slate-800 border-t border-slate-700 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Play/Pause button removed - now handled by VideoPlayer */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsMuted(!isMuted)}
                  className="text-white hover:bg-slate-700"
                >
                  {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                </Button>
                <div className="text-sm text-slate-400">
                  {isOwner ? "You control playback" : "Playback synced with room owner"}
                  {isSyncConnected && (
                    <span className="ml-2 text-green-400">• Cinema Sync Active</span>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant={isPlaying ? "default" : "secondary"}>
                  {isPlaying ? "Playing" : "Paused"}
                </Badge>
                {isOwner && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowSettings(true)}
                    className="text-slate-300 hover:text-white"
                  >
                    <Settings className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="w-80 border-l border-slate-700 bg-slate-800/30 flex flex-col">
          {/* Sidebar Tabs */}
          <div className="border-b border-slate-700 p-4">
            <div className="grid grid-cols-4 gap-1">
              <Button
                variant={showChat ? "default" : "ghost"}
                size="sm"
                onClick={() => {
                  setShowChat(true);
                  setShowMembers(false);
                  setShowVoiceVideo(false);
                  setShowDiagnostics(false);
                }}
                className="text-xs"
              >
                <MessageCircle className="w-3 h-3 mr-1" />
                Chat
              </Button>
              <Button
                variant={showMembers ? "default" : "ghost"}
                size="sm"
                onClick={() => {
                  setShowMembers(true);
                  setShowChat(false);
                  setShowVoiceVideo(false);
                  setShowDiagnostics(false);
                }}
                className="text-xs"
              >
                <Users className="w-3 h-3 mr-1" />
                Members
              </Button>
              <Button
                variant={showVoiceVideo ? "default" : "ghost"}
                size="sm"
                onClick={() => {
                  setShowVoiceVideo(true);
                  setShowChat(false);
                  setShowMembers(false);
                  setShowDiagnostics(false);
                }}
                className="text-xs"
              >
                <Video className="w-3 h-3 mr-1" />
                Voice
              </Button>
              <Button
                variant={showDiagnostics ? "default" : "ghost"}
                size="sm"
                onClick={() => {
                  setShowDiagnostics(true);
                  setShowChat(false);
                  setShowMembers(false);
                  setShowVoiceVideo(false);
                }}
                className="text-xs"
              >
                🔧 Debug
              </Button>
            </div>
          </div>

          {/* Sidebar Content */}
          <div className="flex-1 overflow-hidden">
            {showChat && <RoomChat roomId={roomId || ""} />}
            {showMembers && <RoomMembersList room={room} isOwner={isOwner || false} />}
            {showVoiceVideo && (
              <div className="p-4">
                <JitsiMeetRoom
                  roomId={roomId || ""}
                  isMinimized={isJitsiMinimized}
                  onToggleMinimize={() => setIsJitsiMinimized(!isJitsiMinimized)}
                  onLeave={() => {
                    setShowVoiceVideo(false);
                    setShowChat(true);
                  }}
                />
              </div>
            )}
            {showDiagnostics && (
              <div className="p-4 overflow-auto">
                <ConnectionDiagnostics roomId={roomId} />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Room Settings Dialog */}
      {isOwner && room && (
        <RoomSettingsDialog
          open={showSettings}
          onOpenChange={setShowSettings}
          room={room}
          onRoomUpdated={handleRoomUpdated}
          onRoomDeleted={handleRoomDeleted}
        />
      )}

      {/* Background Refresh Indicator */}
      <BackgroundRefreshIndicator
        isRefreshing={isRefreshing}
        connectionStatus={connectionStatus}
        position="top-right"
        show={true}
      />
    </div>
  );
};

export default RoomPage;
