import express from 'express';
import cors from 'cors';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const app = express();
const PORT = process.env.PORT || 3001;

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// Middleware
app.use(cors());
app.use(express.json());

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Initialize database tables
app.post('/api/init-db', async (req, res) => {
  try {
    // Create rooms table
    const { error: roomsError } = await supabase.rpc('create_rooms_table');
    
    // Create room_members table
    const { error: membersError } = await supabase.rpc('create_room_members_table');
    
    // Create room_invitations table
    const { error: invitationsError } = await supabase.rpc('create_room_invitations_table');

    if (roomsError || membersError || invitationsError) {
      console.error('Database initialization errors:', { roomsError, membersError, invitationsError });
    }

    res.json({ 
      success: true, 
      message: 'Database tables initialized',
      errors: { roomsError, membersError, invitationsError }
    });
  } catch (error) {
    console.error('Error initializing database:', error);
    res.status(500).json({ error: 'Failed to initialize database' });
  }
});

// Get user's rooms
app.get('/api/rooms', async (req, res) => {
  try {
    const userId = req.headers['x-user-id'];
    if (!userId) {
      return res.status(401).json({ error: 'User ID required' });
    }

    const { data, error } = await supabase
      .from('room_members')
      .select(`
        room_id,
        role,
        rooms (
          id,
          name,
          description,
          code,
          owner_id,
          created_at,
          updated_at
        )
      `)
      .eq('user_id', userId);

    if (error) throw error;

    res.json(data);
  } catch (error) {
    console.error('Error fetching rooms:', error);
    res.status(500).json({ error: 'Failed to fetch rooms' });
  }
});

// Create a new room
app.post('/api/rooms', async (req, res) => {
  try {
    const userId = req.headers['x-user-id'];
    const { name, description } = req.body;

    if (!userId) {
      return res.status(401).json({ error: 'User ID required' });
    }

    if (!name) {
      return res.status(400).json({ error: 'Room name required' });
    }

    // Generate unique room code
    const code = Math.random().toString(36).substring(2, 8).toUpperCase();

    // Create room
    const { data: room, error: roomError } = await supabase
      .from('rooms')
      .insert({
        name,
        description,
        code,
        owner_id: userId
      })
      .select()
      .single();

    if (roomError) throw roomError;

    // Add creator as owner member
    const { error: memberError } = await supabase
      .from('room_members')
      .insert({
        room_id: room.id,
        user_id: userId,
        role: 'owner'
      });

    if (memberError) throw memberError;

    res.json(room);
  } catch (error) {
    console.error('Error creating room:', error);
    res.status(500).json({ error: 'Failed to create room' });
  }
});

// Join a room by code
app.post('/api/rooms/join', async (req, res) => {
  try {
    const userId = req.headers['x-user-id'];
    const { code } = req.body;

    if (!userId) {
      return res.status(401).json({ error: 'User ID required' });
    }

    if (!code) {
      return res.status(400).json({ error: 'Room code required' });
    }

    // Find room by code
    const { data: room, error: roomError } = await supabase
      .from('rooms')
      .select('*')
      .eq('code', code.toUpperCase())
      .single();

    if (roomError || !room) {
      return res.status(404).json({ error: 'Room not found' });
    }

    // Check if already a member
    const { data: existingMember } = await supabase
      .from('room_members')
      .select('*')
      .eq('room_id', room.id)
      .eq('user_id', userId)
      .single();

    if (existingMember) {
      return res.json(room); // Already a member
    }

    // Add as member
    const { error: memberError } = await supabase
      .from('room_members')
      .insert({
        room_id: room.id,
        user_id: userId,
        role: 'member'
      });

    if (memberError) throw memberError;

    res.json(room);
  } catch (error) {
    console.error('Error joining room:', error);
    res.status(500).json({ error: 'Failed to join room' });
  }
});

// Get room details
app.get('/api/rooms/:roomId', async (req, res) => {
  try {
    const userId = req.headers['x-user-id'];
    const { roomId } = req.params;

    if (!userId) {
      return res.status(401).json({ error: 'User ID required' });
    }

    // Check if user is a member
    const { data: membership, error: memberError } = await supabase
      .from('room_members')
      .select(`
        role,
        rooms (
          id,
          name,
          description,
          code,
          owner_id,
          created_at,
          updated_at
        )
      `)
      .eq('room_id', roomId)
      .eq('user_id', userId)
      .single();

    if (memberError || !membership) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Get all room members
    const { data: members, error: membersError } = await supabase
      .from('room_members')
      .select('*')
      .eq('room_id', roomId);

    if (membersError) throw membersError;

    const room = membership.rooms;
    res.json({
      ...room,
      members,
      member_count: members.length,
      user_role: membership.role
    });
  } catch (error) {
    console.error('Error fetching room:', error);
    res.status(500).json({ error: 'Failed to fetch room' });
  }
});

// Update room (owner only)
app.put('/api/rooms/:roomId', async (req, res) => {
  try {
    const userId = req.headers['x-user-id'];
    const { roomId } = req.params;
    const { name, description } = req.body;

    if (!userId) {
      return res.status(401).json({ error: 'User ID required' });
    }

    if (!name) {
      return res.status(400).json({ error: 'Room name required' });
    }

    // Check if user is the room owner
    const { data: room, error: roomError } = await supabase
      .from('rooms')
      .select('owner_id')
      .eq('id', roomId)
      .single();

    if (roomError || !room) {
      return res.status(404).json({ error: 'Room not found' });
    }

    if (room.owner_id !== userId) {
      return res.status(403).json({ error: 'Only room owner can update room' });
    }

    // Update room
    const { data: updatedRoom, error: updateError } = await supabase
      .from('rooms')
      .update({
        name,
        description,
        updated_at: new Date().toISOString()
      })
      .eq('id', roomId)
      .select()
      .single();

    if (updateError) throw updateError;

    res.json(updatedRoom);
  } catch (error) {
    console.error('Error updating room:', error);
    res.status(500).json({ error: 'Failed to update room' });
  }
});

// Delete room (owner only)
app.delete('/api/rooms/:roomId', async (req, res) => {
  try {
    const userId = req.headers['x-user-id'];
    const { roomId } = req.params;

    if (!userId) {
      return res.status(401).json({ error: 'User ID required' });
    }

    // Check if user is the room owner
    const { data: room, error: roomError } = await supabase
      .from('rooms')
      .select('owner_id')
      .eq('id', roomId)
      .single();

    if (roomError || !room) {
      return res.status(404).json({ error: 'Room not found' });
    }

    if (room.owner_id !== userId) {
      return res.status(403).json({ error: 'Only room owner can delete room' });
    }

    // Delete room (cascade will handle related records)
    const { error: deleteError } = await supabase
      .from('rooms')
      .delete()
      .eq('id', roomId);

    if (deleteError) throw deleteError;

    res.json({ message: 'Room deleted successfully' });
  } catch (error) {
    console.error('Error deleting room:', error);
    res.status(500).json({ error: 'Failed to delete room' });
  }
});

// Transfer room ownership (owner only)
app.put('/api/rooms/:roomId/transfer-ownership', async (req, res) => {
  try {
    const userId = req.headers['x-user-id'];
    const { roomId } = req.params;
    const { newOwnerId } = req.body;

    if (!userId) {
      return res.status(401).json({ error: 'User ID required' });
    }

    if (!newOwnerId) {
      return res.status(400).json({ error: 'New owner ID required' });
    }

    // Check if user is the room owner
    const { data: room, error: roomError } = await supabase
      .from('rooms')
      .select('owner_id')
      .eq('id', roomId)
      .single();

    if (roomError || !room) {
      return res.status(404).json({ error: 'Room not found' });
    }

    if (room.owner_id !== userId) {
      return res.status(403).json({ error: 'Only room owner can transfer ownership' });
    }

    // Check if new owner is a member of the room
    const { data: newOwnerMember, error: memberError } = await supabase
      .from('room_members')
      .select('*')
      .eq('room_id', roomId)
      .eq('user_id', newOwnerId)
      .single();

    if (memberError || !newOwnerMember) {
      return res.status(400).json({ error: 'New owner must be a member of the room' });
    }

    // Start transaction-like operations
    // Update room owner
    const { error: updateRoomError } = await supabase
      .from('rooms')
      .update({
        owner_id: newOwnerId,
        updated_at: new Date().toISOString()
      })
      .eq('id', roomId);

    if (updateRoomError) throw updateRoomError;

    // Update old owner role to member
    const { error: updateOldOwnerError } = await supabase
      .from('room_members')
      .update({ role: 'member' })
      .eq('room_id', roomId)
      .eq('user_id', userId);

    if (updateOldOwnerError) throw updateOldOwnerError;

    // Update new owner role to owner
    const { error: updateNewOwnerError } = await supabase
      .from('room_members')
      .update({ role: 'owner' })
      .eq('room_id', roomId)
      .eq('user_id', newOwnerId);

    if (updateNewOwnerError) throw updateNewOwnerError;

    res.json({ message: 'Ownership transferred successfully' });
  } catch (error) {
    console.error('Error transferring ownership:', error);
    res.status(500).json({ error: 'Failed to transfer ownership' });
  }
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
});
