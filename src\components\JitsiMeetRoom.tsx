import React, { useEffect, useRef, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Mic, 
  MicOff, 
  Video, 
  VideoOff, 
  Phone, 
  Users, 
  Minimize2, 
  Maximize2,
  Volume2,
  VolumeX,
  Settings
} from 'lucide-react';
import { useJitsiMeet } from '@/hooks/useJitsiMeet';
import { toast } from 'sonner';

interface JitsiMeetRoomProps {
  roomId: string;
  isMinimized?: boolean;
  onToggleMinimize?: () => void;
  onLeave?: () => void;
  className?: string;
}

const JitsiMeetRoom: React.FC<JitsiMeetRoomProps> = ({
  roomId,
  isMinimized = false,
  onToggleMinimize,
  onLeave,
  className = ''
}) => {
  const jitsiContainerRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [showControls, setShowControls] = useState(true);
  
  const {
    jitsiAPI,
    isJitsiLoaded,
    isJitsiConnected,
    participants,
    isAudioMuted,
    isVideoMuted,
    connectionError,
    initializeJitsi,
    disposeJitsi,
    toggleAudio,
    toggleVideo,
    hangUp,
  } = useJitsiMeet();

  // Initialize Jitsi Meet when component mounts
  useEffect(() => {
    if (!isInitialized && jitsiContainerRef.current && roomId) {
      const initJitsi = async () => {
        try {
          await initializeJitsi({
            roomName: `movie-night-${roomId}`,
            parentNode: jitsiContainerRef.current!,
            width: '100%',
            height: isMinimized ? '200px' : '400px',
            configOverwrite: {
              startWithAudioMuted: true,
              startWithVideoMuted: true,
              enableWelcomePage: false,
              enableClosePage: false,
              prejoinPageEnabled: false,
              disableInviteFunctions: true,
              doNotStoreRoom: true,
              disableDeepLinking: true,
              toolbarButtons: isMinimized ? [] : [
                'microphone',
                'camera',
                'chat',
                'participants-pane',
                'tileview',
                'hangup'
              ],
            },
            interfaceConfigOverwrite: {
              DISABLE_JOIN_LEAVE_NOTIFICATIONS: true,
              DISABLE_PRESENCE_STATUS: true,
              HIDE_INVITE_MORE_HEADER: true,
              SHOW_JITSI_WATERMARK: false,
              SHOW_WATERMARK_FOR_GUESTS: false,
              SHOW_BRAND_WATERMARK: false,
              SHOW_POWERED_BY: false,
              TILE_VIEW_MAX_COLUMNS: isMinimized ? 2 : 4,
              TOOLBAR_ALWAYS_VISIBLE: !isMinimized,
              TOOLBAR_TIMEOUT: isMinimized ? 0 : 4000,
            },
          });
          setIsInitialized(true);
        } catch (error) {
          console.error('Failed to initialize Jitsi Meet:', error);
          toast.error('Failed to connect to voice/video chat');
        }
      };

      initJitsi();
    }

    return () => {
      if (isInitialized) {
        disposeJitsi();
        setIsInitialized(false);
      }
    };
  }, [roomId, isMinimized, initializeJitsi, disposeJitsi, isInitialized]);

  // Handle leave room
  const handleLeave = () => {
    hangUp();
    disposeJitsi();
    setIsInitialized(false);
    onLeave?.();
  };

  // Auto-hide controls in minimized mode
  useEffect(() => {
    if (isMinimized) {
      const timer = setTimeout(() => setShowControls(false), 3000);
      return () => clearTimeout(timer);
    } else {
      setShowControls(true);
    }
  }, [isMinimized]);

  if (!roomId) {
    return null;
  }

  return (
    <Card className={`${className} ${isMinimized ? 'h-64' : 'h-96'} transition-all duration-300`}>
      <CardHeader className={`pb-2 ${isMinimized ? 'py-2' : ''}`}>
        <div className="flex items-center justify-between">
          <CardTitle className={`${isMinimized ? 'text-sm' : 'text-lg'} flex items-center gap-2`}>
            <Video className={`${isMinimized ? 'w-4 h-4' : 'w-5 h-5'}`} />
            Voice & Video Chat
            {isJitsiConnected && (
              <Badge variant="secondary" className="ml-2">
                <Users className="w-3 h-3 mr-1" />
                {participants.length + 1}
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {connectionError && (
              <Badge variant="destructive" className="text-xs">
                Connection Error
              </Badge>
            )}
            
            {isJitsiConnected && (
              <Badge variant="default" className="text-xs bg-green-600">
                Connected
              </Badge>
            )}
            
            {onToggleMinimize && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleMinimize}
                className="h-8 w-8 p-0"
              >
                {isMinimized ? (
                  <Maximize2 className="w-4 h-4" />
                ) : (
                  <Minimize2 className="w-4 h-4" />
                )}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className={`${isMinimized ? 'p-2' : 'p-4'} relative`}>
        {/* Jitsi Meet Container */}
        <div 
          ref={jitsiContainerRef}
          className={`w-full bg-slate-900 rounded-lg overflow-hidden ${
            isMinimized ? 'h-40' : 'h-80'
          }`}
          onMouseEnter={() => setShowControls(true)}
          onMouseLeave={() => isMinimized && setTimeout(() => setShowControls(false), 2000)}
        />

        {/* Loading State */}
        {!isJitsiLoaded && (
          <div className="absolute inset-0 flex items-center justify-center bg-slate-900 rounded-lg">
            <div className="text-center">
              <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
              <p className="text-white text-sm">Loading voice/video chat...</p>
            </div>
          </div>
        )}

        {/* Connection Error State */}
        {connectionError && (
          <div className="absolute inset-0 flex items-center justify-center bg-slate-900 rounded-lg">
            <div className="text-center">
              <p className="text-red-400 text-sm mb-2">Failed to connect</p>
              <p className="text-slate-400 text-xs mb-4">{connectionError}</p>
              <Button
                onClick={() => {
                  setIsInitialized(false);
                  window.location.reload();
                }}
                variant="outline"
                size="sm"
              >
                Retry
              </Button>
            </div>
          </div>
        )}

        {/* Custom Controls Overlay */}
        {isJitsiConnected && showControls && (
          <div className={`absolute bottom-2 left-2 right-2 bg-black bg-opacity-70 rounded-lg p-2 transition-opacity duration-300 ${
            showControls ? 'opacity-100' : 'opacity-0'
          }`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleAudio}
                  className={`h-8 w-8 p-0 ${
                    isAudioMuted 
                      ? 'bg-red-600 hover:bg-red-700 text-white' 
                      : 'bg-slate-600 hover:bg-slate-700 text-white'
                  }`}
                >
                  {isAudioMuted ? (
                    <MicOff className="w-4 h-4" />
                  ) : (
                    <Mic className="w-4 h-4" />
                  )}
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleVideo}
                  className={`h-8 w-8 p-0 ${
                    isVideoMuted 
                      ? 'bg-red-600 hover:bg-red-700 text-white' 
                      : 'bg-slate-600 hover:bg-slate-700 text-white'
                  }`}
                >
                  {isVideoMuted ? (
                    <VideoOff className="w-4 h-4" />
                  ) : (
                    <Video className="w-4 h-4" />
                  )}
                </Button>

                {!isMinimized && (
                  <div className="text-white text-xs ml-2">
                    {participants.length + 1} participant{participants.length !== 0 ? 's' : ''}
                  </div>
                )}
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleLeave}
                className="h-8 w-8 p-0 bg-red-600 hover:bg-red-700 text-white"
              >
                <Phone className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Minimized Mode Info */}
        {isMinimized && isJitsiConnected && (
          <div className="absolute top-2 left-2 right-2">
            <div className="flex items-center justify-between text-white text-xs bg-black bg-opacity-50 rounded px-2 py-1">
              <span>Voice/Video Active</span>
              <div className="flex items-center gap-1">
                {isAudioMuted && <MicOff className="w-3 h-3 text-red-400" />}
                {isVideoMuted && <VideoOff className="w-3 h-3 text-red-400" />}
                <Users className="w-3 h-3" />
                <span>{participants.length + 1}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default JitsiMeetRoom;
