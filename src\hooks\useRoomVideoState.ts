
import { useState, useEffect, useCallback, useRef } from 'react';
import { useUser, useAuth } from '@clerk/clerk-react';
import { RoomVideoState, getSupabaseClient } from '@/lib/supabase';
import { toast } from 'sonner';
import { useSmartRefresh } from './useSmartRefresh';
import { RealtimeChannel } from '@supabase/supabase-js';

export interface VideoStateUpdate {
  video_url?: string;
  video_type?: 'youtube' | 'file' | 'url';
  youtube_video_id?: string;
  is_playing?: boolean;
  video_current_time?: number;
  video_duration?: number;
  playback_rate?: number;
}

export interface PreciseSyncData {
  room_id: string;
  is_playing: boolean;
  video_current_time: number;
  video_duration: number;
  playback_rate: number;
  sync_version: number;
  server_time: string;
  last_sync_timestamp: string;
  client_server_diff: number;
  video_url?: string;
  video_type: 'youtube' | 'file' | 'url';
  youtube_video_id?: string;
}

export const useRoomVideoState = (roomId: string, isOwner: boolean) => {
  const { user } = useUser();
  const { getToken, isSignedIn } = useAuth();
  const [videoState, setVideoState] = useState<RoomVideoState | null>(null);
  const [loading, setLoading] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const channelRef = useRef<RealtimeChannel | null>(null);
  const [preciseSyncData, setPreciseSyncData] = useState<PreciseSyncData | null>(null);
  const [lastSyncVersion, setLastSyncVersion] = useState<number>(0);
  const [networkLatency, setNetworkLatency] = useState<number>(0);

  // Fetch current video state
  const fetchVideoState = useCallback(async () => {
    if (!roomId) return;

    try {
      const client = await getSupabaseClient(getToken, isSignedIn);
      const { data, error } = await client
        .from('room_video_state')
        .select('*')
        .eq('room_id', roomId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      setVideoState(data || null);
    } catch (error) {
      console.error('Error fetching video state:', error);
      toast.error('Failed to load video state');
    } finally {
      setLoading(false);
    }
  }, [roomId, getToken, isSignedIn]);

  // Get precise sync data with network compensation
  const getPreciseSyncData = useCallback(async () => {
    if (!roomId) return null;

    try {
      const client = await getSupabaseClient(getToken, isSignedIn);
      const clientTimestamp = new Date().toISOString();

      const { data, error } = await client.rpc('get_precise_sync_time', {
        p_room_id: roomId,
        p_client_timestamp: clientTimestamp
      });

      if (error) throw error;

      if (data && !data.error) {
        setPreciseSyncData(data);
        setNetworkLatency(Math.abs(data.client_server_diff || 0));
        return data;
      }

      return null;
    } catch (error) {
      console.error('Error getting precise sync data:', error);
      return null;
    }
  }, [roomId, getToken, isSignedIn]);

  // Enhanced update function using the database function
  const updateVideoState = useCallback(async (updates: VideoStateUpdate) => {
    if (!user || !roomId || !isOwner) return;

    try {
      const client = await getSupabaseClient(getToken, isSignedIn);

      const { error } = await client.rpc('update_video_sync_state', {
        p_room_id: roomId,
        p_user_id: user.id,
        p_is_playing: updates.is_playing,
        p_current_time: updates.video_current_time,
        p_playback_rate: updates.playback_rate,
        p_video_url: updates.video_url,
        p_video_type: updates.video_type,
        p_youtube_video_id: updates.youtube_video_id,
        p_duration: updates.video_duration
      });

      if (error) throw error;

      // Only refresh if real-time isn't working
      if (connectionStatus !== 'connected') {
        await fetchVideoState();
      }

    } catch (error) {
      console.error('Error updating video state:', error);
      toast.error('Failed to update video state');
    }
  }, [user, roomId, isOwner, getToken, isSignedIn, connectionStatus, fetchVideoState]);

  // Convenience methods for common operations
  const setVideoUrl = useCallback((url: string, type: 'youtube' | 'file' | 'url', youtubeId?: string) => {
    updateVideoState({
      video_url: url,
      video_type: type,
      youtube_video_id: youtubeId,
      is_playing: false,
      video_current_time: 0,
      playback_rate: 1.0
    });
  }, [updateVideoState]);

  const setPlaying = useCallback((playing: boolean, currentTime?: number) => {
    const updates: VideoStateUpdate = { is_playing: playing };
    if (currentTime !== undefined) {
      updates.video_current_time = currentTime;
    }
    updateVideoState(updates);
  }, [updateVideoState]);

  const setCurrentTime = useCallback((time: number) => {
    updateVideoState({ video_current_time: time });
  }, [updateVideoState]);

  const setDuration = useCallback((duration: number) => {
    updateVideoState({ video_duration: duration });
  }, [updateVideoState]);

  const setPlaybackRate = useCallback((rate: number) => {
    updateVideoState({ playback_rate: rate });
  }, [updateVideoState]);

  // Clear video from room
  const clearVideo = useCallback(async () => {
    if (!user || !roomId || !isOwner) {
      console.warn('Cannot clear video: missing user, roomId, or not owner', { user: !!user, roomId, isOwner });
      return;
    }

    try {
      const client = await getSupabaseClient(getToken, isSignedIn);
      console.log('Clearing video for room:', roomId, 'Video state:', videoState);

      // If there's a current video and it's a file upload, try to delete it from storage
      if (videoState?.video_type === 'file' && videoState?.video_url) {
        try {
          console.log('Attempting to delete video file from storage:', videoState.video_url);

          const url = new URL(videoState.video_url);
          console.log('Parsed URL:', { pathname: url.pathname, origin: url.origin });

          const pathParts = url.pathname.split('/').filter(part => part.length > 0);
          console.log('Path parts:', pathParts);

          // More robust path parsing
          // Expected format: /storage/v1/object/public/videos/roomId/filename
          // or: /storage/v1/object/videos/roomId/filename
          let filePath = '';

          if (pathParts.includes('videos')) {
            const videosIndex = pathParts.indexOf('videos');
            if (videosIndex < pathParts.length - 2) {
              const roomFolder = pathParts[videosIndex + 1];
              const fileName = pathParts[videosIndex + 2];
              filePath = `${roomFolder}/${fileName}`;
            }
          }

          if (!filePath) {
            // Fallback: try to extract from the last two parts
            if (pathParts.length >= 2) {
              const fileName = pathParts[pathParts.length - 1];
              const roomFolder = pathParts[pathParts.length - 2];
              filePath = `${roomFolder}/${fileName}`;
            }
          }

          console.log('Computed file path for deletion:', filePath);

          if (!filePath) {
            throw new Error('Could not determine file path from URL');
          }

          const { data: deleteData, error: deleteError } = await client.storage
            .from('videos')
            .remove([filePath]);

          console.log('Storage deletion result:', { data: deleteData, error: deleteError });

          if (deleteError) {
            console.error('Failed to delete video file from storage:', deleteError);
            // Don't throw here - we still want to clear the video state even if file deletion fails
            toast.error(`Warning: Could not delete video file (${deleteError.message}), but video will be removed from room`);
          } else {
            console.log('Successfully deleted video file from storage');
          }
        } catch (storageError) {
          console.error('Error deleting video file:', storageError);
          // Don't throw here - we still want to clear the video state even if file deletion fails
          toast.error(`Warning: Could not delete video file (${storageError instanceof Error ? storageError.message : 'Unknown error'}), but video will be removed from room`);
        }
      }

      // Clear the video state
      console.log('Clearing video state from database...');
      const { error } = await client
        .from('room_video_state')
        .delete()
        .eq('room_id', roomId);

      if (error) {
        console.error('Failed to clear video state:', error);
        throw error;
      }

      console.log('Successfully cleared video state');
      toast.success('Video removed successfully!');
    } catch (error) {
      console.error('Error clearing video:', error);
      toast.error(`Failed to remove video: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [user, roomId, isOwner, getToken, isSignedIn, videoState]);

  // High-frequency precision sync for non-owners
  useEffect(() => {
    if (!roomId || isOwner) return;

    const syncInterval = setInterval(async () => {
      const syncData = await getPreciseSyncData();
      if (syncData && syncData.sync_version > lastSyncVersion) {
        setLastSyncVersion(syncData.sync_version);
      }
    }, 500);

    return () => clearInterval(syncInterval);
  }, [roomId, isOwner, getPreciseSyncData, lastSyncVersion]);

  // Smart background refresh for video state
  useSmartRefresh({
    refreshFunction: fetchVideoState,
    enabled: !!roomId,
    connectionStatus,
    idleThreshold: 5000,
    connectedInterval: 60000, // 1 minute when real-time works
    disconnectedInterval: 10000, // 10 seconds when real-time fails
    debug: false
  });

  // Set up real-time subscription
  useEffect(() => {
    if (!roomId) return;

    let channel: RealtimeChannel | null = null;

    const setupSubscription = async () => {
      try {
        console.log('Setting up video state real-time subscription for room:', roomId);
        setConnectionStatus('connecting');

        // Initial fetch
        await fetchVideoState();

        // Set up real-time subscription
        const client = await getSupabaseClient(getToken, isSignedIn);
        channel = client
          .channel(`room_video_state_${roomId}`)
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'room_video_state',
              filter: `room_id=eq.${roomId}`
            },
            (payload) => {
              console.log('Video state change detected:', payload);
              if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
                const newState = payload.new as RoomVideoState;
                setVideoState(newState);
              } else if (payload.eventType === 'DELETE') {
                setVideoState(null);
              }
            }
          )
          .subscribe((status, err) => {
            console.log('Video state subscription status:', status, err);
            
            if (status === 'SUBSCRIBED') {
              console.log('Successfully subscribed to video state changes');
              setConnectionStatus('connected');
            } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT' || status === 'CLOSED') {
              console.log('Video state connection failed:', status);
              setConnectionStatus('disconnected');
            }
          });

        channelRef.current = channel;
      } catch (error) {
        console.error('Error setting up video state subscription:', error);
        setConnectionStatus('disconnected');
      }
    };

    setupSubscription();

    return () => {
      if (channel) {
        console.log('Cleaning up video state channel');
        channel.unsubscribe();
      }
      channelRef.current = null;
      setConnectionStatus('disconnected');
    };
  }, [roomId, fetchVideoState, getToken, isSignedIn]);

  return {
    videoState,
    loading,
    updateVideoState,
    setVideoUrl,
    setPlaying,
    setCurrentTime,
    setDuration,
    setPlaybackRate,
    clearVideo,
    preciseSyncData,
    getPreciseSyncData,
    networkLatency,
    lastSyncVersion,
    connectionStatus
  };
};
