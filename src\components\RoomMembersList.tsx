
import { useState } from "react";
import { useUser } from "@clerk/clerk-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Crown, MoreVertical, UserMinus, UserPlus, Shield } from "lucide-react";
import { toast } from "sonner";

interface RoomMember {
  id: string;
  user_id: string;
  user_name: string;
  user_email?: string;
  user_image?: string;
  role: 'owner' | 'member';
  joined_at: string;
}

interface Room {
  id: string;
  name: string;
  description?: string;
  code: string;
  owner_id: string;
  user_role: 'owner' | 'member';
  member_count: number;
  created_at: string;
  updated_at: string;
  members?: RoomMember[];
}

interface RoomMembersListProps {
  room: Room;
  isOwner: boolean;
}

const RoomMembersList = ({ room, isOwner }: RoomMembersListProps) => {
  const { user } = useUser();
  const [memberToRemove, setMemberToRemove] = useState<RoomMember | null>(null);
  const [memberToPromote, setMemberToPromote] = useState<RoomMember | null>(null);

  // Mock members data - in a real app, this would come from the room data
  const members: RoomMember[] = room.members || [
    {
      id: "1",
      user_id: room.owner_id,
      user_name: user?.fullName || user?.firstName || "Room Owner",
      user_email: user?.primaryEmailAddress?.emailAddress,
      user_image: user?.imageUrl,
      role: "owner",
      joined_at: room.created_at,
    },
  ];

  const handleRemoveMember = async (member: RoomMember) => {
    try {
      // TODO: Implement member removal logic
      toast.success(`${member.user_name} has been removed from the room`);
      setMemberToRemove(null);
    } catch (error) {
      toast.error("Failed to remove member");
    }
  };

  const handlePromoteMember = async (member: RoomMember) => {
    try {
      // TODO: Implement member promotion logic
      toast.success(`${member.user_name} has been promoted to admin`);
      setMemberToPromote(null);
    } catch (error) {
      toast.error("Failed to promote member");
    }
  };

  const handleInviteMembers = () => {
    // TODO: Open invite dialog
    toast.info("Invite feature coming soon!");
  };

  const formatJoinDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-slate-700">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-semibold text-white">Members ({members.length})</h3>
          {isOwner && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleInviteMembers}
              className="text-slate-300 hover:text-white hover:bg-slate-700"
            >
              <UserPlus className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {members.map((member) => (
          <Card key={member.id} className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-slate-600 rounded-full flex items-center justify-center">
                    {member.user_image ? (
                      <img
                        src={member.user_image}
                        alt={member.user_name}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    ) : (
                      <span className="text-sm font-medium text-white">
                        {member.user_name.charAt(0).toUpperCase()}
                      </span>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium text-white truncate">
                        {member.user_name}
                      </p>
                      {member.role === "owner" && (
                        <Crown className="w-3 h-3 text-amber-400" />
                      )}
                      <Badge
                        variant={member.role === "owner" ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {member.role}
                      </Badge>
                    </div>
                    <p className="text-xs text-slate-400 truncate">
                      Joined {formatJoinDate(member.joined_at)}
                    </p>
                  </div>
                </div>

                {isOwner && member.user_id !== user?.id && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-slate-400 hover:text-white"
                      >
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="bg-slate-800 border-slate-700">
                      {member.role === "member" && (
                        <DropdownMenuItem
                          onClick={() => setMemberToPromote(member)}
                          className="text-slate-300 hover:text-white hover:bg-slate-700"
                        >
                          <Shield className="w-4 h-4 mr-2" />
                          Promote to Admin
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem
                        onClick={() => setMemberToRemove(member)}
                        className="text-red-400 hover:text-red-300 hover:bg-red-900/20"
                      >
                        <UserMinus className="w-4 h-4 mr-2" />
                        Remove from Room
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            </CardContent>
          </Card>
        ))}

        {members.length === 1 && (
          <div className="text-center py-8">
            <p className="text-slate-400 text-sm mb-2">No other members yet</p>
            {isOwner && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleInviteMembers}
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                <UserPlus className="w-4 h-4 mr-2" />
                Invite Members
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Remove Member Dialog */}
      <AlertDialog open={!!memberToRemove} onOpenChange={() => setMemberToRemove(null)}>
        <AlertDialogContent className="bg-slate-800 border-slate-700">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">Remove Member</AlertDialogTitle>
            <AlertDialogDescription className="text-slate-300">
              Are you sure you want to remove {memberToRemove?.user_name} from this room?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="border-slate-600 text-slate-300 hover:bg-slate-700">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => memberToRemove && handleRemoveMember(memberToRemove)}
              className="bg-red-600 hover:bg-red-700"
            >
              Remove
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Promote Member Dialog */}
      <AlertDialog open={!!memberToPromote} onOpenChange={() => setMemberToPromote(null)}>
        <AlertDialogContent className="bg-slate-800 border-slate-700">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white">Promote Member</AlertDialogTitle>
            <AlertDialogDescription className="text-slate-300">
              Are you sure you want to promote {memberToPromote?.user_name} to admin?
              They will have elevated permissions in this room.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="border-slate-600 text-slate-300 hover:bg-slate-700">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => memberToPromote && handlePromoteMember(memberToPromote)}
            >
              Promote
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default RoomMembersList;
