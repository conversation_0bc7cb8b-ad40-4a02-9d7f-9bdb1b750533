# YouTube API Troubleshooting Guide

## Common Issues

### 1. Certificate and CORS Errors

If you're seeing errors like:
- `ERR_CERT_COMMON_NAME_INVALID`
- `Access to script at 'https://youtube.com/iframe_api' from origin 'http://localhost:8080' has been blocked by CORS policy`

This is because YouTube's iframe API has strict security requirements.

## Solutions

### Option 1: Use HTTPS Development Server (Recommended)

Run the development server with HTTPS:

```bash
npm run dev:https
```

This will:
- Start the development server with a self-signed certificate on `https://localhost:8082/`
- Allow YouTube iframe API to load properly
- You may need to accept the browser security warning for localhost
- Navigate to the HTTPS URL and accept the certificate warning when prompted

### Option 2: Fallback Mode (Automatic)

The application automatically switches to fallback mode when the YouTube API fails to load:

1. **Automatic Fallback**: After 3 failed attempts, the app switches to a basic YouTube embed
2. **Manual Retry**: Click the "Retry" button in fallback mode to try the API again
3. **Limited Features**: Fallback mode has limited programmatic control but videos still play

### Option 3: Accept Browser Security Warnings

If using HTTP development server:

1. Navigate to `https://www.youtube.com/iframe_api` in your browser
2. Accept any security warnings
3. Refresh your application

## Technical Details

### Why This Happens

- YouTube's iframe API requires secure contexts (HTTPS) for many features
- Modern browsers block mixed content (HTTPS resources on HTTP pages)
- Certificate validation is strict for external APIs

### Fallback Implementation

The app includes a robust fallback system:

```typescript
// Automatic fallback after API failure
if (youTubeApiStatus === 'failed') {
  return <YouTubeFallbackEmbed />;
}
```

### Development vs Production

- **Development**: Use `npm run dev:https` for full API functionality
- **Production**: HTTPS is required anyway, so no issues

## Browser-Specific Notes

### Chrome
- Strictest security policies
- May require manual certificate acceptance

### Firefox
- More lenient with localhost certificates
- Usually works with basic HTTPS setup

### Safari
- Similar to Chrome
- May need additional certificate configuration

## Testing the Fix

To test if the YouTube API is working:

1. Start the HTTPS development server: `npm run dev:https`
2. Navigate to `https://localhost:8082/` (accept certificate warning)
3. Create or join a room
4. Try adding a YouTube video URL
5. Check browser console - you should see "YouTube player ready" instead of errors

## Need Help?

If you're still experiencing issues:

1. Check browser console for specific error messages
2. Try the HTTPS development server
3. Verify your YouTube video URLs are valid
4. Check if your network blocks YouTube API requests
5. Try the fallback mode if API continues to fail
