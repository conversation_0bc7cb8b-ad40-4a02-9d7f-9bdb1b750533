import React from 'react';
import { Wifi, WifiOff, AlertCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ConnectionStatusProps {
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  className?: string;
  showText?: boolean;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ 
  status, 
  className,
  showText = false 
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'connecting':
        return {
          icon: Loader2,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-500/10',
          text: 'Connecting...',
          animate: 'animate-spin'
        };
      case 'connected':
        return {
          icon: Wifi,
          color: 'text-green-500',
          bgColor: 'bg-green-500/10',
          text: 'Connected',
          animate: ''
        };
      case 'disconnected':
        return {
          icon: WifiOff,
          color: 'text-gray-500',
          bgColor: 'bg-gray-500/10',
          text: 'Disconnected',
          animate: ''
        };
      case 'error':
        return {
          icon: AlertCircle,
          color: 'text-red-500',
          bgColor: 'bg-red-500/10',
          text: 'Connection Error',
          animate: ''
        };
      default:
        return {
          icon: WifiOff,
          color: 'text-gray-500',
          bgColor: 'bg-gray-500/10',
          text: 'Unknown',
          animate: ''
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  if (showText) {
    return (
      <div className={cn(
        'flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium',
        config.bgColor,
        config.color,
        className
      )}>
        <Icon className={cn('w-4 h-4', config.animate)} />
        <span>{config.text}</span>
      </div>
    );
  }

  return (
    <div className={cn(
      'flex items-center justify-center w-6 h-6 rounded-full',
      config.bgColor,
      className
    )}>
      <Icon className={cn('w-4 h-4', config.color, config.animate)} />
    </div>
  );
};

export default ConnectionStatus;
