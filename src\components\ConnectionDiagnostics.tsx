import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Wifi, 
  WifiOff, 
  AlertCircle, 
  CheckCircle, 
  Loader2,
  RefreshCw,
  Bug
} from 'lucide-react';
import { useAuth } from '@clerk/clerk-react';
import { getSupabaseClient } from '@/lib/supabase';
import { testStorageAccess } from '@/utils/storage-test';

interface ConnectionDiagnosticsProps {
  roomId?: string;
  className?: string;
}

interface DiagnosticResult {
  test: string;
  status: 'pending' | 'success' | 'error' | 'warning';
  message: string;
  details?: any;
}

export const ConnectionDiagnostics: React.FC<ConnectionDiagnosticsProps> = ({ 
  roomId, 
  className 
}) => {
  const { getToken, isSignedIn, user } = useAuth();
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<DiagnosticResult[]>([]);

  const addResult = (result: DiagnosticResult) => {
    setResults(prev => [...prev, result]);
  };

  const runDiagnostics = async () => {
    setIsRunning(true);
    setResults([]);

    try {
      // Test 1: Authentication Status
      addResult({
        test: 'Authentication',
        status: isSignedIn ? 'success' : 'error',
        message: isSignedIn ? `Signed in as ${user?.firstName || 'User'}` : 'Not authenticated',
        details: { isSignedIn, userId: user?.id }
      });

      // Test 2: Supabase Client
      try {
        const client = await getSupabaseClient(getToken, isSignedIn);
        addResult({
          test: 'Supabase Client',
          status: 'success',
          message: 'Client initialized successfully',
          details: { hasClient: !!client }
        });

        // Test 3: Database Connection
        try {
          const { data, error } = await client.from('rooms').select('count').limit(1);
          addResult({
            test: 'Database Connection',
            status: error ? 'error' : 'success',
            message: error ? `Database error: ${error.message}` : 'Database connection successful',
            details: { error, data }
          });
        } catch (dbError) {
          addResult({
            test: 'Database Connection',
            status: 'error',
            message: `Database connection failed: ${dbError}`,
            details: { dbError }
          });
        }

        // Test 4: Real-time Connection
        try {
          const testChannel = client.channel('diagnostic-test');
          let realtimeStatus = 'pending';
          
          const statusPromise = new Promise<string>((resolve) => {
            testChannel.subscribe((status, err) => {
              if (status === 'SUBSCRIBED') {
                resolve('success');
              } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT' || status === 'CLOSED') {
                resolve('error');
              }
            });
            
            // Timeout after 10 seconds
            setTimeout(() => {
              if (realtimeStatus === 'pending') {
                resolve('error');
              }
            }, 10000);
          });

          realtimeStatus = await statusPromise;
          testChannel.unsubscribe();

          addResult({
            test: 'Real-time Connection',
            status: realtimeStatus === 'success' ? 'success' : 'error',
            message: realtimeStatus === 'success' ? 'Real-time connection successful' : 'Real-time connection failed',
            details: { realtimeStatus }
          });
        } catch (realtimeError) {
          addResult({
            test: 'Real-time Connection',
            status: 'error',
            message: `Real-time connection error: ${realtimeError}`,
            details: { realtimeError }
          });
        }

        // Test 5: Storage Access
        try {
          const storageResult = await testStorageAccess(getToken, isSignedIn);
          addResult({
            test: 'Storage Access',
            status: storageResult.success ? 'success' : 'error',
            message: storageResult.success ? 'Storage access successful' : `Storage access failed: ${storageResult.error}`,
            details: storageResult
          });
        } catch (storageError) {
          addResult({
            test: 'Storage Access',
            status: 'error',
            message: `Storage test failed: ${storageError}`,
            details: { storageError }
          });
        }

        // Test 6: Room Access (if roomId provided)
        if (roomId) {
          try {
            const { data: roomData, error: roomError } = await client
              .from('room_members')
              .select('role, rooms(id, name)')
              .eq('room_id', roomId)
              .eq('user_id', user?.id)
              .single();

            addResult({
              test: 'Room Access',
              status: roomError ? 'error' : 'success',
              message: roomError ? `Room access error: ${roomError.message}` : `Room access successful (${roomData?.role})`,
              details: { roomError, roomData }
            });
          } catch (roomAccessError) {
            addResult({
              test: 'Room Access',
              status: 'error',
              message: `Room access test failed: ${roomAccessError}`,
              details: { roomAccessError }
            });
          }
        }

      } catch (clientError) {
        addResult({
          test: 'Supabase Client',
          status: 'error',
          message: `Client initialization failed: ${clientError}`,
          details: { clientError }
        });
      }

    } catch (error) {
      addResult({
        test: 'General',
        status: 'error',
        message: `Diagnostic failed: ${error}`,
        details: { error }
      });
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'pending':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
    }
  };

  const getStatusColor = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'pending':
        return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bug className="w-5 h-5" />
          Connection Diagnostics
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={runDiagnostics} 
          disabled={isRunning}
          className="w-full"
        >
          {isRunning ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Running Diagnostics...
            </>
          ) : (
            <>
              <RefreshCw className="w-4 h-4 mr-2" />
              Run Diagnostics
            </>
          )}
        </Button>

        {results.length > 0 && (
          <div className="space-y-2">
            {results.map((result, index) => (
              <div 
                key={index}
                className={`p-3 rounded-lg border ${getStatusColor(result.status)}`}
              >
                <div className="flex items-center gap-2 mb-1">
                  {getStatusIcon(result.status)}
                  <span className="font-medium text-sm">{result.test}</span>
                </div>
                <p className="text-sm">{result.message}</p>
                {result.details && (
                  <details className="mt-2">
                    <summary className="text-xs cursor-pointer">Details</summary>
                    <pre className="text-xs mt-1 p-2 bg-black/5 rounded overflow-auto">
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ConnectionDiagnostics;
