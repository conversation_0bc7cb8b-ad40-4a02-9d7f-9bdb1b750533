import { validateVideoFile, formatFileSize, getEstimatedUploadTime, MAX_VIDEO_SIZE } from '../file-validation';

// Mock File constructor for testing
class MockFile {
  name: string;
  size: number;
  type: string;

  constructor(name: string, size: number, type: string) {
    this.name = name;
    this.size = size;
    this.type = type;
  }
}

describe('file-validation', () => {
  describe('validateVideoFile', () => {
    it('should accept valid video files', () => {
      const file = new MockFile('test.mp4', 1000000, 'video/mp4') as File;
      const result = validateVideoFile(file);
      
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should reject non-video files', () => {
      const file = new MockFile('test.txt', 1000000, 'text/plain') as File;
      const result = validateVideoFile(file);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Please select a valid video file.');
    });

    it('should reject files exceeding size limit', () => {
      const file = new MockFile('large.mp4', MAX_VIDEO_SIZE + 1, 'video/mp4') as File;
      const result = validateVideoFile(file);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('exceeds the maximum limit');
    });

    it('should warn about large files', () => {
      const file = new MockFile('large.mp4', 200000000, 'video/mp4') as File; // 200MB
      const result = validateVideoFile(file);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Large file detected');
    });

    it('should warn about unsupported formats', () => {
      const file = new MockFile('test.xyz', 1000000, 'video/xyz') as File;
      const result = validateVideoFile(file);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings?.[0]).toContain('may not be supported');
    });
  });

  describe('formatFileSize', () => {
    it('should format bytes correctly', () => {
      expect(formatFileSize(0)).toBe('0 Bytes');
      expect(formatFileSize(1024)).toBe('1 KB');
      expect(formatFileSize(1048576)).toBe('1 MB');
      expect(formatFileSize(1073741824)).toBe('1 GB');
    });

    it('should handle decimal places', () => {
      expect(formatFileSize(1536)).toBe('1.5 KB');
      expect(formatFileSize(1572864)).toBe('1.5 MB');
    });
  });

  describe('getEstimatedUploadTime', () => {
    it('should estimate time for small files', () => {
      const result = getEstimatedUploadTime(1048576); // 1MB
      expect(result).toBe('~1 seconds');
    });

    it('should estimate time for medium files', () => {
      const result = getEstimatedUploadTime(104857600); // 100MB
      expect(result).toBe('~2 minutes');
    });

    it('should estimate time for large files', () => {
      const result = getEstimatedUploadTime(5368709120); // 5GB
      expect(result).toBe('~2 hours');
    });
  });
});
