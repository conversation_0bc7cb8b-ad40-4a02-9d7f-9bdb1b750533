import { useState, useEffect, useCallback, useRef } from 'react';
import { useUser, useAuth } from '@clerk/clerk-react';
import { supabase, createAuthenticatedSupabaseClient } from '@/lib/supabase';
import { toast } from 'sonner';

export interface VideoSyncState {
  room_id: string;
  video_url?: string;
  video_type: 'youtube' | 'file' | 'url';
  youtube_video_id?: string;
  is_playing: boolean;
  video_current_time: number;
  video_duration: number;
  playback_rate: number;
  sync_version: number;
  last_updated: string;
  updated_by: string;
}

export interface SyncCommand {
  type: 'play' | 'pause' | 'seek' | 'rate_change' | 'video_change';
  timestamp: number;
  data: any;
  sync_id: string;
}

export interface CinemaSync {
  server_time: number;
  client_time: number;
  network_latency: number;
  sync_accuracy: number;
  last_sync: number;
}

export const useVideoSync = (roomId: string, isOwner: boolean) => {
  const { user } = useUser();
  const { getToken, isSignedIn } = useAuth();
  const [syncState, setSyncState] = useState<VideoSyncState | null>(null);
  const [cinemaSync, setCinemaSync] = useState<CinemaSync>({
    server_time: 0,
    client_time: 0,
    network_latency: 0,
    sync_accuracy: 0,
    last_sync: 0
  });
  const [isConnected, setIsConnected] = useState(false);
  const [syncChannel, setSyncChannel] = useState<any>(null);
  const syncIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const commandQueueRef = useRef<SyncCommand[]>([]);

  // Get authenticated Supabase client
  const getAuthenticatedClient = useCallback(async () => {
    if (!isSignedIn) return supabase;
    
    try {
      const token = await getToken({ template: 'supabase' });
      return token ? createAuthenticatedSupabaseClient(token) : supabase;
    } catch (error) {
      console.error('Error getting authenticated client:', error);
      return supabase;
    }
  }, [getToken, isSignedIn]);

  // Calculate precise server time with network compensation
  const calculateServerTime = useCallback(async () => {
    const client = await getAuthenticatedClient();
    const clientStart = performance.now();
    
    try {
      const { data, error } = await client.rpc('get_server_time');
      const clientEnd = performance.now();
      
      if (error) throw error;
      
      const networkLatency = (clientEnd - clientStart) / 2;
      const serverTime = new Date(data).getTime();
      const clientTime = Date.now();
      const compensatedServerTime = serverTime + networkLatency;
      
      setCinemaSync(prev => ({
        ...prev,
        server_time: compensatedServerTime,
        client_time: clientTime,
        network_latency: networkLatency,
        last_sync: Date.now()
      }));
      
      return {
        serverTime: compensatedServerTime,
        networkLatency,
        clientTime
      };
    } catch (error) {
      console.error('Error calculating server time:', error);
      return null;
    }
  }, [getAuthenticatedClient]);

  // Send sync command (owner only)
  const sendSyncCommand = useCallback(async (command: Omit<SyncCommand, 'timestamp' | 'sync_id'>) => {
    if (!isOwner || !user || !syncChannel) return;

    const timeData = await calculateServerTime();
    if (!timeData) return;

    const syncCommand: SyncCommand = {
      ...command,
      timestamp: timeData.serverTime,
      sync_id: `${user.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };

    try {
      // Send via Supabase Realtime Broadcast for instant delivery
      await syncChannel.send({
        type: 'broadcast',
        event: 'video_sync_command',
        payload: syncCommand
      });

      console.log('Sync command sent:', syncCommand);
    } catch (error) {
      console.error('Error sending sync command:', error);
      toast.error('Failed to sync video state');
    }
  }, [isOwner, user, syncChannel, calculateServerTime]);

  // Process sync command (viewers only)
  const processSyncCommand = useCallback((command: SyncCommand) => {
    if (isOwner) return; // Owners don't process commands

    const now = Date.now();
    const commandAge = now - command.timestamp;
    
    // Ignore commands older than 5 seconds
    if (commandAge > 5000) {
      console.log('Ignoring old sync command:', commandAge);
      return;
    }

    // Add to command queue for processing
    commandQueueRef.current.push(command);
    
    // Process queue immediately
    processCommandQueue();
  }, [isOwner]);

  // Process command queue with timing compensation
  const processCommandQueue = useCallback(() => {
    const commands = commandQueueRef.current.splice(0); // Clear queue
    if (commands.length === 0) return;

    // Sort commands by timestamp
    commands.sort((a, b) => a.timestamp - b.timestamp);

    commands.forEach(command => {
      const { type, data, timestamp } = command;
      const currentTime = Date.now();
      const delay = Math.max(0, timestamp - currentTime + cinemaSync.network_latency);

      setTimeout(() => {
        switch (type) {
          case 'play':
            // Calculate current video time with compensation
            const playTime = data.currentTime + ((Date.now() - timestamp) / 1000);
            window.dispatchEvent(new CustomEvent('video-sync-play', { 
              detail: { currentTime: playTime, accuracy: delay } 
            }));
            break;
            
          case 'pause':
            window.dispatchEvent(new CustomEvent('video-sync-pause', { 
              detail: { currentTime: data.currentTime, accuracy: delay } 
            }));
            break;
            
          case 'seek':
            window.dispatchEvent(new CustomEvent('video-sync-seek', { 
              detail: { currentTime: data.currentTime, accuracy: delay } 
            }));
            break;
            
          case 'rate_change':
            window.dispatchEvent(new CustomEvent('video-sync-rate', { 
              detail: { rate: data.rate, accuracy: delay } 
            }));
            break;
            
          case 'video_change':
            window.dispatchEvent(new CustomEvent('video-sync-change', { 
              detail: { ...data, accuracy: delay } 
            }));
            break;
        }
        
        // Update sync accuracy
        setCinemaSync(prev => ({
          ...prev,
          sync_accuracy: delay
        }));
      }, delay);
    });
  }, [cinemaSync.network_latency]);

  // Owner control functions
  const playVideo = useCallback((currentTime: number) => {
    sendSyncCommand({
      type: 'play',
      data: { currentTime }
    });
  }, [sendSyncCommand]);

  const pauseVideo = useCallback((currentTime: number) => {
    sendSyncCommand({
      type: 'pause',
      data: { currentTime }
    });
  }, [sendSyncCommand]);

  const seekVideo = useCallback((currentTime: number) => {
    sendSyncCommand({
      type: 'seek',
      data: { currentTime }
    });
  }, [sendSyncCommand]);

  const changePlaybackRate = useCallback((rate: number) => {
    sendSyncCommand({
      type: 'rate_change',
      data: { rate }
    });
  }, [sendSyncCommand]);

  const changeVideo = useCallback((videoData: any) => {
    sendSyncCommand({
      type: 'video_change',
      data: videoData
    });
  }, [sendSyncCommand]);

  // Set up real-time sync channel
  useEffect(() => {
    if (!roomId) return;

    const setupSyncChannel = async () => {
      try {
        const client = await getAuthenticatedClient();
        
        const channel = client
          .channel(`video-sync:${roomId}`)
          .on('broadcast', { event: 'video_sync_command' }, (payload) => {
            console.log('Received sync command:', payload);
            processSyncCommand(payload.payload);
          })
          .on('presence', { event: 'sync' }, (payload) => {
            console.log('Sync presence update:', payload);
          })
          .subscribe((status) => {
            console.log('Video sync channel status:', status);
            setIsConnected(status === 'SUBSCRIBED');
            
            if (status === 'SUBSCRIBED') {
              setSyncChannel(channel);
              toast.success('Video sync connected');
            } else if (status === 'CHANNEL_ERROR') {
              toast.error('Video sync connection error');
            }
          });

        return () => {
          client.removeChannel(channel);
        };
      } catch (error) {
        console.error('Error setting up sync channel:', error);
        toast.error('Failed to connect video sync');
      }
    };

    const cleanup = setupSyncChannel();
    return () => cleanup?.then(fn => fn?.());
  }, [roomId, getAuthenticatedClient, processSyncCommand]);

  // High-frequency sync for cinema experience (viewers only)
  useEffect(() => {
    if (isOwner || !isConnected) return;

    syncIntervalRef.current = setInterval(async () => {
      await calculateServerTime();
    }, 1000); // Sync every second for smooth experience

    return () => {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
      }
    };
  }, [isOwner, isConnected, calculateServerTime]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (syncChannel) {
        supabase.removeChannel(syncChannel);
      }
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
      }
    };
  }, [syncChannel]);

  return {
    // State
    syncState,
    cinemaSync,
    isConnected,
    
    // Owner controls
    playVideo,
    pauseVideo,
    seekVideo,
    changePlaybackRate,
    changeVideo,
    
    // Utilities
    calculateServerTime,
    sendSyncCommand,
  };
};
