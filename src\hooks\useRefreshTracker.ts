import { useState, useCallback, useRef } from 'react';

interface RefreshState {
  isRefreshing: boolean;
  lastRefreshTime: number | null;
  refreshCount: number;
}

/**
 * Hook to track background refresh operations across the app.
 * Provides a way to show subtle indicators when data is being refreshed.
 */
export const useRefreshTracker = () => {
  const [refreshState, setRefreshState] = useState<RefreshState>({
    isRefreshing: false,
    lastRefreshTime: null,
    refreshCount: 0
  });

  const refreshTimeoutRef = useRef<NodeJS.Timeout>();

  const startRefresh = useCallback(() => {
    // Clear any existing timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    setRefreshState(prev => ({
      isRefreshing: true,
      lastRefreshTime: Date.now(),
      refreshCount: prev.refreshCount + 1
    }));

    // Auto-stop refresh indication after 3 seconds
    refreshTimeoutRef.current = setTimeout(() => {
      setRefreshState(prev => ({
        ...prev,
        isRefreshing: false
      }));
    }, 3000);
  }, []);

  const stopRefresh = useCallback(() => {
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    setRefreshState(prev => ({
      ...prev,
      isRefreshing: false
    }));
  }, []);

  const wrapRefreshFunction = useCallback(<T extends any[], R>(
    fn: (...args: T) => Promise<R> | R
  ) => {
    return async (...args: T): Promise<R> => {
      startRefresh();
      try {
        const result = await fn(...args);
        return result;
      } finally {
        // Don't immediately stop - let the timeout handle it for better UX
      }
    };
  }, [startRefresh]);

  return {
    ...refreshState,
    startRefresh,
    stopRefresh,
    wrapRefreshFunction
  };
};
