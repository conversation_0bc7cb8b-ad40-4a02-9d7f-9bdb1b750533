-- Enhanced Cinema Sync with Jitsi Meet Integration
-- This migration adds support for real-time video synchronization with Jitsi Meet

-- Create enhanced video sync state table
CREATE TABLE IF NOT EXISTS video_sync_state (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    room_id UUID NOT NULL REFERENCES rooms(id) ON DELETE CASCADE,
    video_url TEXT,
    video_type TEXT CHECK (video_type IN ('youtube', 'file', 'url')) DEFAULT 'youtube',
    youtube_video_id TEXT,
    is_playing BOOLEAN DEFAULT false,
    video_current_time DECIMAL(10,3) DEFAULT 0,
    video_duration DECIMAL(10,3) DEFAULT 0,
    playback_rate DECIMAL(3,2) DEFAULT 1.0,
    sync_version INTEGER DEFAULT 1,
    server_timestamp TIMESTAMPTZ DEFAULT NOW(),
    updated_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(room_id)
);

-- Create sync commands table for real-time commands
CREATE TABLE IF NOT EXISTS sync_commands (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    room_id UUID NOT NULL REFERENCES rooms(id) ON DELETE CASCADE,
    command_type TEXT NOT NULL CHECK (command_type IN ('play', 'pause', 'seek', 'rate_change', 'video_change')),
    command_data JSONB NOT NULL,
    sync_id TEXT NOT NULL UNIQUE,
    server_timestamp TIMESTAMPTZ DEFAULT NOW(),
    client_timestamp TIMESTAMPTZ,
    executed_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_video_sync_state_room_id ON video_sync_state(room_id);
CREATE INDEX IF NOT EXISTS idx_sync_commands_room_id ON sync_commands(room_id);
CREATE INDEX IF NOT EXISTS idx_sync_commands_timestamp ON sync_commands(server_timestamp);

-- Enable RLS
ALTER TABLE video_sync_state ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_commands ENABLE ROW LEVEL SECURITY;

-- RLS Policies for video_sync_state
CREATE POLICY "Users can view video sync state for rooms they're members of" ON video_sync_state
    FOR SELECT USING (
        room_id IN (
            SELECT room_id FROM room_members 
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );

CREATE POLICY "Room owners can update video sync state" ON video_sync_state
    FOR ALL USING (
        room_id IN (
            SELECT id FROM rooms 
            WHERE owner_id = auth.uid()
        )
    );

-- RLS Policies for sync_commands
CREATE POLICY "Users can view sync commands for rooms they're members of" ON sync_commands
    FOR SELECT USING (
        room_id IN (
            SELECT room_id FROM room_members 
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );

CREATE POLICY "Room owners can insert sync commands" ON sync_commands
    FOR INSERT WITH CHECK (
        room_id IN (
            SELECT id FROM rooms 
            WHERE owner_id = auth.uid()
        )
    );

-- Function to get server time with high precision
CREATE OR REPLACE FUNCTION get_server_time()
RETURNS TIMESTAMPTZ
LANGUAGE SQL
STABLE
AS $$
    SELECT NOW();
$$;

-- Function to update video sync state with precision timing
CREATE OR REPLACE FUNCTION update_cinema_sync_state(
    p_room_id UUID,
    p_user_id UUID,
    p_video_url TEXT DEFAULT NULL,
    p_video_type TEXT DEFAULT NULL,
    p_youtube_video_id TEXT DEFAULT NULL,
    p_is_playing BOOLEAN DEFAULT NULL,
    p_current_time DECIMAL DEFAULT NULL,
    p_duration DECIMAL DEFAULT NULL,
    p_playback_rate DECIMAL DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_room_exists BOOLEAN;
    v_is_owner BOOLEAN;
    v_sync_version INTEGER;
    v_result JSONB;
BEGIN
    -- Check if room exists and user is owner
    SELECT 
        EXISTS(SELECT 1 FROM rooms WHERE id = p_room_id),
        EXISTS(SELECT 1 FROM rooms WHERE id = p_room_id AND owner_id = p_user_id)
    INTO v_room_exists, v_is_owner;
    
    IF NOT v_room_exists THEN
        RETURN jsonb_build_object('error', 'Room not found');
    END IF;
    
    IF NOT v_is_owner THEN
        RETURN jsonb_build_object('error', 'Only room owner can update video state');
    END IF;
    
    -- Insert or update video sync state
    INSERT INTO video_sync_state (
        room_id,
        video_url,
        video_type,
        youtube_video_id,
        is_playing,
        video_current_time,
        video_duration,
        playback_rate,
        sync_version,
        server_timestamp,
        updated_by,
        updated_at
    ) VALUES (
        p_room_id,
        COALESCE(p_video_url, ''),
        COALESCE(p_video_type, 'youtube'),
        p_youtube_video_id,
        COALESCE(p_is_playing, false),
        COALESCE(p_current_time, 0),
        COALESCE(p_duration, 0),
        COALESCE(p_playback_rate, 1.0),
        1,
        NOW(),
        p_user_id,
        NOW()
    )
    ON CONFLICT (room_id) DO UPDATE SET
        video_url = COALESCE(p_video_url, video_sync_state.video_url),
        video_type = COALESCE(p_video_type, video_sync_state.video_type),
        youtube_video_id = COALESCE(p_youtube_video_id, video_sync_state.youtube_video_id),
        is_playing = COALESCE(p_is_playing, video_sync_state.is_playing),
        video_current_time = COALESCE(p_current_time, video_sync_state.video_current_time),
        video_duration = COALESCE(p_duration, video_sync_state.video_duration),
        playback_rate = COALESCE(p_playback_rate, video_sync_state.playback_rate),
        sync_version = video_sync_state.sync_version + 1,
        server_timestamp = NOW(),
        updated_by = p_user_id,
        updated_at = NOW()
    RETURNING sync_version INTO v_sync_version;
    
    -- Return success with sync data
    SELECT jsonb_build_object(
        'success', true,
        'sync_version', v_sync_version,
        'server_time', NOW(),
        'room_id', p_room_id
    ) INTO v_result;
    
    RETURN v_result;
END;
$$;

-- Function to get precise sync data for cinema experience
CREATE OR REPLACE FUNCTION get_cinema_sync_data(
    p_room_id UUID,
    p_client_timestamp TIMESTAMPTZ DEFAULT NOW()
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_sync_data JSONB;
    v_server_time TIMESTAMPTZ;
    v_client_server_diff INTERVAL;
BEGIN
    v_server_time := NOW();
    v_client_server_diff := v_server_time - p_client_timestamp;
    
    -- Get current sync state
    SELECT jsonb_build_object(
        'room_id', room_id,
        'video_url', video_url,
        'video_type', video_type,
        'youtube_video_id', youtube_video_id,
        'is_playing', is_playing,
        'video_current_time', video_current_time,
        'video_duration', video_duration,
        'playback_rate', playback_rate,
        'sync_version', sync_version,
        'server_time', v_server_time,
        'last_sync_timestamp', server_timestamp,
        'client_server_diff', EXTRACT(EPOCH FROM v_client_server_diff),
        'network_latency', EXTRACT(EPOCH FROM v_client_server_diff) / 2
    )
    FROM video_sync_state
    WHERE room_id = p_room_id
    INTO v_sync_data;
    
    IF v_sync_data IS NULL THEN
        RETURN jsonb_build_object(
            'error', 'No video sync data found for room',
            'server_time', v_server_time
        );
    END IF;
    
    RETURN v_sync_data;
END;
$$;

-- Function to record sync command
CREATE OR REPLACE FUNCTION record_sync_command(
    p_room_id UUID,
    p_command_type TEXT,
    p_command_data JSONB,
    p_sync_id TEXT,
    p_client_timestamp TIMESTAMPTZ DEFAULT NOW()
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_result JSONB;
BEGIN
    -- Insert sync command
    INSERT INTO sync_commands (
        room_id,
        command_type,
        command_data,
        sync_id,
        server_timestamp,
        client_timestamp,
        executed_by
    ) VALUES (
        p_room_id,
        p_command_type,
        p_command_data,
        p_sync_id,
        NOW(),
        p_client_timestamp,
        auth.uid()
    );
    
    RETURN jsonb_build_object(
        'success', true,
        'server_time', NOW(),
        'sync_id', p_sync_id
    );
END;
$$;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_video_sync_state_updated_at 
    BEFORE UPDATE ON video_sync_state 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON video_sync_state TO authenticated;
GRANT ALL ON sync_commands TO authenticated;
GRANT EXECUTE ON FUNCTION get_server_time() TO authenticated;
GRANT EXECUTE ON FUNCTION update_cinema_sync_state(UUID, UUID, TEXT, TEXT, TEXT, BOOLEAN, DECIMAL, DECIMAL, DECIMAL) TO authenticated;
GRANT EXECUTE ON FUNCTION get_cinema_sync_data(UUID, TIMESTAMPTZ) TO authenticated;
GRANT EXECUTE ON FUNCTION record_sync_command(UUID, TEXT, JSONB, TEXT, TIMESTAMPTZ) TO authenticated;
