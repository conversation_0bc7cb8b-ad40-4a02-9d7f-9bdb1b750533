
import { useState, useEffect, useCallback, useRef } from 'react';
import { useUser, useAuth } from '@clerk/clerk-react';
import { RoomMessage, getSupabaseClient } from '@/lib/supabase';
import { toast } from 'sonner';
import { RealtimeChannel } from '@supabase/supabase-js';
import { useSmartRefresh } from './useSmartRefresh';

interface TypingUser {
  id: string;
  name: string;
  timestamp: number;
}

interface OnlineUser {
  id: string;
  name: string;
  image?: string;
  joinedAt: string;
}

export const useRoomMessages = (roomId: string) => {
  const { user } = useUser();
  const { getToken, isSignedIn } = useAuth();
  const [messages, setMessages] = useState<RoomMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([]);
  const channelRef = useRef<RealtimeChannel | null>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');

  // Fetch existing messages
  const fetchMessages = useCallback(async () => {
    if (!roomId) return;

    try {
      const client = await getSupabaseClient(getToken, isSignedIn);
      const { data, error } = await client
        .from('room_messages')
        .select('*')
        .eq('room_id', roomId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      setMessages(data || []);
    } catch (error) {
      console.error('Error fetching messages:', error);
      toast.error('Failed to load messages');
    } finally {
      setLoading(false);
    }
  }, [roomId, getToken, isSignedIn]);

  // Send typing indicator
  const sendTypingIndicator = useCallback(async (isTyping: boolean) => {
    if (!user || !roomId || !channelRef.current) return;

    try {
      await channelRef.current.send({
        type: 'broadcast',
        event: 'typing',
        payload: {
          user_id: user.id,
          user_name: user.fullName || user.firstName || 'Anonymous',
          is_typing: isTyping,
          timestamp: Date.now()
        }
      });
    } catch (error) {
      console.error('Error sending typing indicator:', error);
    }
  }, [user, roomId]);

  // Send a new message
  const sendMessage = useCallback(async (content: string) => {
    if (!user || !roomId || !content.trim()) return;

    try {
      // Stop typing indicator
      await sendTypingIndicator(false);

      const client = await getSupabaseClient(getToken, isSignedIn);
      const messageData = {
        room_id: roomId,
        user_id: user.id,
        user_name: user.fullName || user.firstName || 'Anonymous',
        user_image: user.imageUrl,
        content: content.trim(),
        message_type: 'message' as const
      };

      const { data, error } = await client
        .from('room_messages')
        .insert(messageData)
        .select()
        .single();

      if (error) throw error;

      // Only add message if real-time didn't already add it
      if (connectionStatus !== 'connected') {
        setMessages(prev => [...prev, data]);
      }

    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    }
  }, [user, roomId, getToken, isSignedIn, sendTypingIndicator, connectionStatus]);

  // Clean up typing users periodically
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      setTypingUsers(prev => prev.filter(user => now - user.timestamp < 3000));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Smart background refresh for messages
  useSmartRefresh({
    refreshFunction: fetchMessages,
    enabled: !!roomId,
    connectionStatus,
    idleThreshold: 15000,
    connectedInterval: 600000, // 10 minutes when real-time works
    disconnectedInterval: 30000, // 30 seconds when real-time fails
    debug: false
  });

  // Set up real-time subscription
  useEffect(() => {
    if (!roomId || !user) return;

    let channel: RealtimeChannel | null = null;

    const setupSubscription = async () => {
      try {
        console.log('Setting up real-time subscription for room:', roomId);
        setConnectionStatus('connecting');

        // Initial fetch
        await fetchMessages();

        // Get the appropriate client (authenticated if possible)
        const client = await getSupabaseClient(getToken, isSignedIn);
        
        channel = client
          .channel(`room_messages_${roomId}`, {
            config: {
              broadcast: { self: true },
              presence: { key: user.id }
            }
          })
          // Listen to database changes with more specific filter
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'room_messages',
              filter: `room_id=eq.${roomId}`
            },
            (payload) => {
              console.log('Real-time message received:', payload);
              const newMessage = payload.new as RoomMessage;
              
              setMessages(prev => {
                // Check if message already exists
                if (prev.some(msg => msg.id === newMessage.id)) {
                  return prev;
                }
                return [...prev, newMessage];
              });
            }
          )
          // Listen to typing indicators
          .on('broadcast', { event: 'typing' }, (payload) => {
            const { user_id, user_name, is_typing, timestamp } = payload.payload;

            if (user_id === user.id) return; // Ignore own typing

            setTypingUsers(prev => {
              const filtered = prev.filter(u => u.id !== user_id);
              if (is_typing) {
                return [...filtered, { id: user_id, name: user_name, timestamp }];
              }
              return filtered;
            });
          })
          // Listen to presence changes
          .on('presence', { event: 'sync' }, () => {
            const state = channel?.presenceState();
            if (state) {
              const users: OnlineUser[] = [];
              Object.keys(state).forEach(userId => {
                const presences = state[userId];
                if (presences.length > 0) {
                  const presence = presences[0];
                  users.push({
                    id: userId,
                    name: presence.name || 'Anonymous',
                    image: presence.image,
                    joinedAt: presence.joined_at || new Date().toISOString()
                  });
                }
              });
              setOnlineUsers(users);
            }
          })
          .subscribe((status, err) => {
            console.log('Messages channel subscription status:', status, err);
            
            if (status === 'SUBSCRIBED') {
              console.log('Successfully subscribed to messages channel');
              setConnectionStatus('connected');
              
              // Track user presence
              channel?.track({
                name: user.fullName || user.firstName || 'Anonymous',
                image: user.imageUrl,
                joined_at: new Date().toISOString()
              });
            } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT' || status === 'CLOSED') {
              console.log('Messages channel connection failed:', status);
              setConnectionStatus('disconnected');
            }
          });

        channelRef.current = channel;

      } catch (error) {
        console.error('Error setting up messages subscription:', error);
        setConnectionStatus('disconnected');
      }
    };

    setupSubscription();

    return () => {
      if (channel) {
        console.log('Cleaning up messages channel subscription');
        channel.untrack();
        channel.unsubscribe();
      }
      channelRef.current = null;
      setConnectionStatus('disconnected');
    };
  }, [roomId, user, fetchMessages, getToken, isSignedIn]);

  // Handle typing with debounce
  const handleTyping = useCallback(() => {
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Send typing indicator
    sendTypingIndicator(true);

    // Stop typing after 2 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      sendTypingIndicator(false);
    }, 2000);
  }, [sendTypingIndicator]);

  // Stop typing
  const stopTyping = useCallback(() => {
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
    sendTypingIndicator(false);
  }, [sendTypingIndicator]);

  return {
    messages,
    loading,
    sendMessage,
    typingUsers,
    onlineUsers,
    handleTyping,
    stopTyping,
    connectionStatus
  };
};
