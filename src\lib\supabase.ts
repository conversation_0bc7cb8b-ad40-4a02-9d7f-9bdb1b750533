
import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Use the values directly since Lovable doesn't use .env files
const supabaseUrl = 'https://ttcapwgcfadajcoljuuk.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR0Y2Fwd2djZmFkYWpjb2xqdXVrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzgzMDA3NzIsImV4cCI6MjA1Mzg3Njc3Mn0.htrkudTGkcjeUKcPHTjnmT_fkFWpE-YfChMRhtDlUpA';

// Create the base Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

// Single authenticated client instance to prevent multiple instances
let authenticatedClient: SupabaseClient | null = null;
let currentToken: string | null = null;

// Function to create an authenticated Supabase client with Clerk token
export const createAuthenticatedSupabaseClient = (token: string): SupabaseClient => {
  // Reuse existing client if token hasn't changed
  if (authenticatedClient && currentToken === token) {
    return authenticatedClient;
  }

  // Clean up old client
  if (authenticatedClient) {
    authenticatedClient.removeAllChannels();
  }

  currentToken = token;
  authenticatedClient = createClient(supabaseUrl, supabaseAnonKey, {
    global: {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
    realtime: {
      params: {
        eventsPerSecond: 10
      }
    }
  });

  return authenticatedClient;
};

// Helper to get the current authenticated client or base client
export const getSupabaseClient = async (getToken?: () => Promise<string | null>, isSignedIn?: boolean): Promise<SupabaseClient> => {
  if (isSignedIn && getToken) {
    try {
      const token = await getToken({ template: 'supabase' });
      if (token) {
        return createAuthenticatedSupabaseClient(token);
      }
    } catch (error) {
      console.error('Error getting authenticated client:', error);
    }
  }
  return supabase;
};

// Database types
export interface Room {
  id: string;
  name: string;
  description?: string;
  code: string;
  owner_id: string;
  created_at: string;
  updated_at: string;
}

export interface RoomMember {
  id: string;
  room_id: string;
  user_id: string;
  role: 'owner' | 'member';
  joined_at: string;
}

export interface RoomInvitation {
  id: string;
  room_id: string;
  email: string;
  invited_by: string;
  status: 'pending' | 'accepted' | 'declined';
  created_at: string;
  expires_at: string;
}

export interface RoomMessage {
  id: string;
  room_id: string;
  user_id: string;
  user_name: string;
  user_image?: string;
  content: string;
  message_type: 'message' | 'system';
  created_at: string;
}

export interface RoomVideoState {
  id: string;
  room_id: string;
  video_url?: string;
  video_type: 'youtube' | 'file' | 'url';
  youtube_video_id?: string;
  is_playing: boolean;
  video_current_time: number;
  video_duration: number;
  playback_rate: number;
  last_sync_timestamp: string;
  sync_version: number;
  updated_by: string;
  updated_at: string;
}
